# AIBoB 聊天机器人功能说明

## 功能概述

### 🪙 金币系统
- **获得金币方式**：
  - 每日签到：基础10金币 + 排名奖励 + 连续签到奖励
  - UNO游戏获胜：50金币
  - Wordle游戏获胜：30金币（同时记录完成时间到排行榜）
  - 成语接龙获胜：第1名100金币，第2名60金币，第3名30金币
- **查看金币**：使用 `coins` 命令查看当前金币数量
- **身份绑定**：金币与用户昵称和识别码(trip)绑定，确保安全性
- **金币存储**：所有用户数据存储在 `userdata/user_data.json` 文件中

### 📅 每日签到系统
- **基础奖励**：每日签到获得10金币
- **排名奖励**：按签到时间排序，前三名获得额外奖励
  - 🥇 第1名：+20金币（总共30金币）
  - 🥈 第2名：+15金币（总共25金币）
  - 🥉 第3名：+10金币（总共20金币）
- **连续签到奖励**：
  - 连续3天：+5金币
  - 连续7天：+15金币
  - 连续15天：+30金币
  - 连续30天：+50金币
- **签到命令**：`ping` 或 `签到`
- **翻倍卡效果**：使用签到金币翻倍卡可使所有奖励翻倍

### 🎒 背包系统
- **查看背包**：使用 `bag` 或 `背包` 命令
- **道具类型**：
  - 签到金币翻倍卡：下次签到获得的金币翻倍
  - 成语接龙跳过卡：在成语接龙游戏中跳过一次轮次
  - Wordle提示卡：在Wordle游戏中获得一个字母位置提示
- **使用方法**：
  - 翻倍卡：拥有时下次签到自动生效
  - 跳过卡：在成语接龙游戏中输入 `skip`
  - 提示卡：在Wordle游戏中输入 `hint`

### 🛒 商店系统
- **查看商店**：使用 `shop` 命令查看所有可购买物品
- **数字购买**：使用 `shop buy <商品号> <参数>` 购买物品（如：`shop buy 1 我的欢迎语`）
- **购买记录**：每次购买生成唯一流水单号，可用 `shop history` 查看
- **更新物品**：使用 `shop update <物品> <参数>` 更新已购买物品
- **开关功能**：使用 `shop toggle <物品> <开关>` 开启/关闭功能
- **身份验证**：需要使用正确的昵称和识别码(trip)才能购买

### 🏆 排名系统
- **金币排行榜**：使用 `rank` 命令查看金币排行榜（前10名）
- **Wordle计时排行榜**：使用 `rank wordle` 查看Wordle完成时间排行榜（时间越短越好）
- **成语接龙排行榜**：使用 `rank idiom` 查看成语接龙轮次排行榜（轮次越多越好）
- **个人排名**：使用 `rank <用户名>` 查看指定用户的金币排名
- **排名显示**：🥇🥈🥉 分别表示前三名

### 🀄 成语接龙游戏
- **游戏规则**：2-8人参与，用前一个成语的最后一个字开头接龙
- **时间限制**：每人限时30秒，超时淘汰
- **奖励机制**：需要3人或以上才有金币奖励
  - 🥇 第1名：100金币
  - 🥈 第2名：60金币
  - 🥉 第3名：30金币

### 🎁 可购买物品

#### 1. 自定义欢迎语
- **价格**：100金币，更新价格：1金币
- **功能**：设置专属的个人欢迎语，当你进入聊天室时会在公开聊天中显示自定义欢迎语
- **使用方法**：
  - 购买：`buy 1 你的欢迎语`
  - 更新：`wel 新的欢迎语`（仅需1金币）
  - 开启：`wel on`
  - 关闭：`wel off`

#### 2. 签到金币翻倍卡
- **价格**：150金币
- **功能**：使用后下次签到获得的金币翻倍（包括基础奖励、排名奖励、连续奖励）
- **使用次数**：一次性消耗品
- **购买方法**：`buy 2`

#### 3. 成语接龙跳过卡
- **价格**：80金币
- **功能**：在成语接龙游戏中可以跳过一次轮次，不会被淘汰
- **使用方法**：在轮到自己时输入 `skip` 命令
- **购买方法**：`buy 3`

#### 4. Wordle提示卡
- **价格**：60金币
- **功能**：在Wordle游戏中获得一个字母的位置提示
- **使用方法**：在游戏中输入 `hint` 命令
- **购买方法**：`buy 4`

#### 5. 神秘礼盒
- **价格**：100金币
- **功能**：随机获得奖励或空奖励（50%概率）
- **可能奖励**：
  - 金币（20-200金币随机）
  - 签到翻倍卡 x1
  - 成语接龙跳过卡 x1
  - Wordle提示卡 x1
  - 自定义欢迎语（如果未拥有）
- **购买方法**：`buy 5`

## 🎨 Wordle图片改进

### 视觉改进
- **字体升级**：
  - 优先使用项目内置的LXGWWenKai字体（支持中英文，类似宋体效果）
  - 跨平台兼容：Windows/Linux/macOS都能正常显示
  - 备选字体链：Times New Roman → Georgia → Arial → Linux系统字体 → 默认字体
- **圆角方框**：使用圆角矩形替代原来的直角矩形，更加美观
- **尺寸优化**：
  - 单元格大小：60px → 70px
  - 边距：5px → 8px
  - 边框宽度：2px → 3px
  - 字体大小：32px → 36px
- **居中优化**：改进文字居中算法，考虑字体基线，使字母更好地居中显示

## 📁 文件结构

### 新增文件
- `userdata/user_data.json` - 用户数据和金币存储
- `userdata/ai/` - AI聊天历史记录存储目录
- `modules/idiom_game.py` - 成语接龙游戏模块
- `fonts/LXGWWenKai-Regular.ttf` - 项目内置字体文件
- `FEATURES_README.md` - 本说明文档
- `FILE_NAMING_GUIDE.md` - 文件命名规范指南

### 修改文件
- `modules/user_system.py` - 扩展用户系统，添加金币、商店和排名功能
- `modules/wordle_image.py` - 改进Wordle图片生成，添加圆角矩形和更好的字体
- `modules/ai.py` - 修改AI历史记录存储路径到 `userdata/ai/`
- `main.py` - 添加金币奖励逻辑、商店命令、排名系统和成语接龙游戏处理
- `config/help.json` - 添加金币、商店、排名和成语接龙的帮助信息

## 🎮 游戏奖励机制

### UNO游戏
- 当玩家出完所有牌获胜时，自动获得50金币奖励
- 奖励消息会在聊天室中公开显示

### Wordle游戏
- 当玩家成功猜出单词时，自动获得30金币奖励
- 奖励消息会在聊天室中公开显示

## 💡 使用示例

```
# 每日签到
ping
签到

# 查看背包
bag
背包

# 查看金币
coins

# 查看排行榜
rank

# 查看个人排名
rank Alice

# 查看商店
shop

# 购买商品
buy 1 欢迎来到我的世界！      # 购买自定义欢迎语
buy 2                        # 购买签到金币翻倍卡
buy 3                        # 购买成语接龙跳过卡
buy 4                        # 购买Wordle提示卡
buy 5                        # 购买神秘礼盒

# 欢迎语管理
wel 这是我的新欢迎语！       # 更新欢迎语（1金币）
wel off                      # 关闭自定义欢迎语
wel on                       # 重新开启

# 成语接龙游戏
idiom join          # 加入游戏
idiom start         # 开始游戏
idiom status        # 查看状态
四面楚歌            # 直接输入成语接龙
skip               # 使用跳过卡

# Wordle游戏
wordle start       # 开始游戏
APPLE             # 猜测单词
hint              # 使用提示卡

# 查看帮助
help checkin
help inventory
help coins
help rank
help shop
help idiom
```

## 🔧 技术实现

### 数据存储
- 使用JSON格式存储用户数据
- 自动创建必要的目录和文件
- 支持数据迁移和向后兼容

### 错误处理
- 完善的错误处理机制
- 金币不足时的友好提示
- 文件操作异常的安全处理

### 性能优化
- 延迟加载用户数据
- 批量保存减少I/O操作
- 内存中缓存常用数据

## 📁 目录结构变更

### AI历史记录迁移
- 原路径：`userdata/*.json`
- 新路径：`userdata/ai/*.json`
- 自动迁移：系统会自动将现有的AI聊天历史文件迁移到新目录

### 新的目录结构
```
AIBoB/
├── fonts/                       # 字体文件目录
│   └── LXGWWenKai-Regular.ttf  # 项目内置字体
├── data/
│   └── games/
│       ├── wordle_answers.txt   # Wordle答案题库
│       ├── wordle_valid_guesses.txt # Wordle有效猜测词库
│       └── idiom.json          # 成语数据库
├── userdata/
│   ├── ai/                     # AI聊天历史记录
│   │   ├── user1_chat_history.json
│   │   └── user2_chat_history.json
│   └── user_data.json         # 用户数据和金币信息
└── config/                    # 配置文件目录
```

## 🚀 未来扩展

系统设计支持轻松添加新的商店物品和金币获得方式：
- 新的虚拟物品
- 更多游戏奖励
- 每日签到奖励
- 成就系统
- 更多成语接龙游戏模式
