{"main": {"title": "**BoB 机器人帮助**", "sections": {"**独立指令**": ["`@BoB <消息>` 或 `@BoB_xxxx <消息>` - AI对话", "`ping` / `签到` - 每日签到", "`coins` - 查看金币数量", "`bag` / `背包` - 查看背包", "`wel <内容>` - 更新欢迎语", "`history [数量]` - 查看聊天历史", "`msg <用户> <内容>` - 给用户留言", "`delete` - 删除AI聊天记录"], "**带有子指令或参数的指令头**": ["`uno` - UNO游戏系统", "`wordle` - <PERSON><PERSON>猜词游戏", "`idiom` - 成语接龙游戏", "`shop` - 商店购买系统", "`rank` - 排名查看系统", "`stock` - 股票交易系统"], "**详细帮助**": ["直接输入指令头查看详细说明", "例如: `uno`, `shop`, `rank`, `stock`"], "**完整指令列表**": ["输入 `all` 查看完整指令列表"]}}, "uno": {"title": "**UNO游戏**", "sections": {"**基本命令**": ["`uno join` - 加入游戏", "`uno start` - 开始游戏(需要2-8人)", "`uno play <牌名>` - 出牌", "`uno draw` - 摸牌", "`uno uno` - 喊UNO", "`uno hand` - 查看手牌", "`uno status` - 查看游戏状态", "`uno leave` - 离开游戏", "`uno reset` - 重置游戏"], "**出牌示例**": ["`uno play 红5`", "`uno play 蓝跳过`", "`uno play 变色 绿` - 万能牌需指定颜色"], "**游戏规则**": ["出牌必须与顶牌颜色或数字相同", "手牌剩1张时必须喊UNO", "获胜奖励: **50金币**"]}}, "wordle": {"title": "**Wordle游戏**", "sections": {"**基本命令**": ["`wordle start` - 开始游戏", "`APPLE` - 直接输入5字母单词猜测", "`hint` - 使用提示卡", "`wordle status` - 查看游戏状态"], "**游戏规则**": ["猜测5个字母的英文单词", "共有6次猜测机会", "每次猜测后会显示图片反馈", "获胜奖励: **30金币**"], "**图片颜色含义**": ["**绿色背景** = 字母位置正确", "**黄色背景** = 字母存在但位置错误", "**灰色背景** = 字母不存在于答案中"]}}, "shop": {"title": "**商店系统**", "sections": {"**基本命令**": ["`shop list` - 查看商店物品列表", "`buy <ID> [参数]` - 购买商品", "`shop history` - 查看购买历史"], "**商品列表**": ["`buy 1 我的欢迎语` - 自定义欢迎语(**100金币**)", "`buy 2` - 签到金币翻倍卡(**150金币**)", "`buy 3` - 成语接龙跳过卡(**80金币**)", "`buy 4` - <PERSON><PERSON>提示卡(**60金币**)", "`buy 5` - 神秘礼盒(**100金币**)"], "**注意事项**": ["欢迎语更新: `wel 新内容` (1金币)", "欢迎语开关: `wel on/off`", "购买需要正确的昵称和识别码", "每次购买生成唯一流水号"]}}, "idiom": {"title": "**成语接龙游戏**", "sections": {"**基本命令**": ["`idiom join` - 加入游戏", "`idiom start` - 开始游戏(需要2-8人)", "`idiom status` - 查看游戏状态", "`idiom leave` - 离开游戏", "`idiom reset` - 重置游戏"], "**游戏规则**": ["直接输入**4个中文字符**的成语进行接龙", "需要**拼音相同**即可，不需要字完全相同", "例如: 咄咄逼人 → 任何'ren'音开头的成语", "每人限时**30秒**，超时淘汰", "参与人数: 2-8人，需要**3人以上**才有金币奖励"], "**奖励机制**": ["**第1名**: 100金币", "**第2名**: 60金币", "**第3名**: 30金币", "`skip` - 使用跳过卡(需先购买)"]}}, "rank": {"title": "**排名系统**", "sections": {"**基本命令**": ["`rank list` - 查看排行榜类型说明", "`rank 1` - 金币排行榜(前10名)", "`rank 2` - <PERSON><PERSON>计时排行榜", "`rank 3` - 成语接龙轮次排行榜", "`rank <用户名>` - 查看指定用户排名"], "**排行榜说明**": ["**金币排行榜**: 按金币数量排序", "**Wordle排行榜**: 按完成时间排序(越短越好)", "**成语接龙排行榜**: 按轮次数量排序(越多越好)", "前三名显示: 🥇🥈🥉"]}}, "stock": {"title": "**股票交易系统**", "sections": {"**简化指令**": ["`get <代码> <数量>` - 买入股票 (如: get 1 10)", "`sell <代码> <数量>` - 卖出股票 (如: sell 1 5)", "`portfolio` - 查看投资组合", "`price <代码>` - 查看股票详情 (如: price 1)"], "**原始指令**": ["`stock` - 查看股票列表和帮助", "`stock list` - 查看所有可交易股票", "`stock get <代码> <数量>` - 买入股票", "`stock sell <代码> <数量>` - 卖出股票", "`stock portfolio` - 查看投资组合", "`stock price <代码>` - 查看股票详情"], "**股票代码**": ["**科技股**: 1(苹果), 2(微软), 3(谷歌), 4(特斯拉), 5(英伟达), 6(<PERSON><PERSON>)", "**中国公司**: 7(阿里巴巴), 8(百度), 9(京东), 10(腾讯), 11(拼多多), 12(蔚来)", "**其他公司**: 13(亚马逊), 14(奈飞), 15(AMD)"], "**交易说明**": ["• 使用虚拟金币进行交易", "• 股价基于真实公司但为模拟数据", "• 价格每30秒自动更新", "• 推荐使用简化指令：get/sell/portfolio/price"]}}, "all": {"title": "**完整指令列表**", "sections": {"**说明**": ["以下是所有可用指令的详细说明", "建议收藏此页面以便随时查阅"]}}}