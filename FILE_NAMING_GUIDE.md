# 文件命名规范指南

## 概述

本文档说明了AIBoB项目中的文件命名约定，确保文件名清晰、直观且易于理解。

## 📁 目录结构和命名

### 主要目录
```
AIBoB/
├── config/          # 配置文件目录
├── data/           # 数据文件目录
│   └── games/      # 游戏相关数据
├── fonts/          # 字体文件目录
├── modules/        # Python模块目录
├── userdata/       # 用户数据目录
│   └── ai/         # AI聊天历史记录
├── history/        # 聊天历史记录
├── temp/           # 临时文件目录
└── __pycache__/    # Python缓存目录
```

## 📄 文件命名约定

### 游戏数据文件 (`data/games/`)

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `wordle_answers.txt` | Wordle答案题库 | 包含可能作为答案的5字母英文单词 |
| `wordle_valid_guesses.txt` | Wordle有效猜测词库 | 包含所有可接受的猜测单词 |
| `idiom.json` | 成语数据库 | 包含成语接龙游戏的成语数据 |

**命名规则**：
- 使用下划线分隔单词
- 功能在前，类型在后
- 明确区分用途（answers vs valid_guesses）

### 字体文件 (`fonts/`)

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `LXGWWenKai-Regular.ttf` | 项目内置字体 | 支持中英文的开源字体，确保跨平台兼容性 |

**命名规则**：
- 保持原始字体名称
- 使用标准的字体文件扩展名（.ttf, .otf等）

### 配置文件 (`config/`)

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `help.json` | 帮助系统配置 | 存储各种命令的帮助信息 |
| `user_config.json` | 用户系统配置 | 存储已知用户列表和欢迎消息 |
| `messages.json` | 留言系统数据 | 存储用户间的留言信息 |
| `reconnect_config.json` | 重连配置 | 存储自动重连相关设置 |
| `welcome_messages.json` | 欢迎消息配置 | 存储随机欢迎消息列表 |

**命名规则**：
- 使用下划线分隔单词
- 以功能命名，添加`_config`后缀表示配置文件

### Python模块 (`modules/`)

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `ai.py` | AI聊天功能 | 处理AI对话和历史记录 |
| `user_system.py` | 用户系统 | 用户管理、金币系统、商店功能 |
| `wordle_game.py` | Wordle游戏逻辑 | Wordle游戏的核心逻辑 |
| `wordle_image.py` | Wordle图片生成 | 生成Wordle游戏的可视化图片 |
| `uno_game.py` | UNO游戏逻辑 | UNO卡牌游戏的核心逻辑 |
| `idiom_game.py` | 成语接龙游戏 | 成语接龙游戏的核心逻辑 |
| `html_generator.py` | HTML生成器 | 生成聊天历史的HTML页面 |
| `file_upload.py` | 文件上传功能 | 处理文件上传到图床 |

**命名规则**：
- 使用下划线分隔单词
- 功能模块以功能命名
- 游戏模块以`游戏名_game.py`格式命名

### 用户数据文件 (`userdata/`)

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `user_data.json` | 用户数据和金币 | 存储用户金币、购买记录等 |
| `ai/用户名_chat_history.json` | AI聊天历史 | 每个用户的AI对话历史 |

**命名规则**：
- 主数据文件使用描述性名称
- 个人文件使用`用户名_类型.json`格式

### 文档文件

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `FEATURES_README.md` | 功能说明文档 | 详细说明所有功能和使用方法 |
| `FILE_NAMING_GUIDE.md` | 文件命名规范 | 本文档，说明命名约定 |
| `requirements.txt` | Python依赖列表 | 项目所需的Python包 |

**命名规则**：
- 重要文档使用全大写
- 使用下划线分隔单词
- README文件添加功能前缀

## 🔄 重命名历史

### 已完成的重命名

| 旧文件名 | 新文件名 | 原因 |
|----------|----------|------|
| `wordle_words.txt` | `wordle_answers.txt` | 明确表示这是答案题库 |
| `valid-wordle-words.txt` | `wordle_valid_guesses.txt` | 明确表示这是有效猜测词库，统一使用下划线 |
| `COIN_SYSTEM_README.md` | `FEATURES_README.md` | 扩展为全功能说明文档 |

### 清理的文件

- `history/main.py` - 重复的主程序文件
- `history/wordle_words.txt` - 重复的Wordle文件
- `history/valid-wordle-words.txt` - 重复的Wordle文件
- `__pycache__/trip_collector.cpython-313.pyc` - 已删除模块的缓存

## 💡 命名最佳实践

### ✅ 好的命名示例
- `wordle_answers.txt` - 清楚表明是Wordle的答案文件
- `user_system.py` - 明确的功能模块名
- `idiom_game.py` - 清楚的游戏模块名

### ❌ 避免的命名
- `words.txt` - 太模糊，不知道是什么单词
- `valid.txt` - 不知道验证什么
- `data.json` - 太通用，不知道存储什么数据

### 命名原则
1. **描述性** - 文件名应该清楚说明文件的用途
2. **一致性** - 同类文件使用相同的命名模式
3. **简洁性** - 在保持清晰的前提下尽量简短
4. **可读性** - 使用下划线分隔单词，避免驼峰命名
5. **分类性** - 相关文件使用相同的前缀或后缀

## 🚀 未来扩展

当添加新功能时，请遵循以下命名约定：

- **新游戏**: `游戏名_game.py`
- **新数据文件**: `功能_数据类型.txt/json`
- **新配置文件**: `功能_config.json`
- **新文档**: `功能_README.md`

这样可以确保项目文件结构始终保持清晰和一致。
