import json
import time
import random
import subprocess
import websocket
import threading
import os
import string
from modules.uno_game import UnoGame
from modules.wordle_game import WordleGame
from modules.wordle_image import generate_wordle_image
from modules.idiom_game import IdiomGame

from modules.html_generator import process_history_request
from modules.user_system import initialize_user_system, user_system
from modules.stock_game import stock_game

def generate_bot_info():
    """生成机器人昵称和识别码"""
    import random
    
    # 随机选择一个美观的词汇作为后缀
    suffix = random.choice(NICKNAME_WORDS)
    full_nickname = f"{BOT_BASE_NAME}_{suffix}"
    
    return full_nickname, BOT_TRIP_CODE

def log_message(msg_type, content, nick=None):
    """统一的日志输出函数"""
    timestamp = time.strftime("%H:%M:%S")

    if msg_type == "chat":
        print(f"[{timestamp}] CHAT {nick}: {content}")
    elif msg_type == "join":
        print(f"[{timestamp}] JOIN {nick} 加入房间")
    elif msg_type == "leave":
        print(f"[{timestamp}] LEAVE {nick} 离开房间")
    elif msg_type == "whisper":
        print(f"[{timestamp}] WHISPER 私聊: {content}")
    elif msg_type == "bot_send":
        # 取消bot_send日志输出，避免重复记录
        pass
    elif msg_type == "error":
        print(f"[{timestamp}] ERROR 错误: {content}")
    elif msg_type == "info":
        print(f"[{timestamp}] INFO {content}")
    elif msg_type == "game":
        print(f"[{timestamp}] GAME 游戏: {content}")
    else:
        print(f"[{timestamp}] {msg_type.upper()}: {content}")

def sendd(text, customId=None):
    message = {"cmd": "chat", "text": str(text)}
    if customId:
        message["customId"] = customId
    return json.dumps(message)

def update(text, customId):
    return json.dumps({"cmd": "updateMessage", "text": str(text), "mode": "overwrite", "customId": customId})



# 全局游戏实例
uno_game = UnoGame()
wordle_game = WordleGame()
idiom_game = IdiomGame()

# 全局WebSocket实例
ws = None

# 简化的重连配置
ENABLE_RECONNECT = True
reconnect_running = True
first_connection = True

# 机器人信息
BOT_BASE_NAME = "BoB"
BOT_TRIP_CODE = "8888"
BOT_CURRENT_NICKNAME = ""  # 当前会话的完整昵称，在连接时设置

# 美观的昵称后缀词汇库（2-5个字母，首字母大写）
NICKNAME_WORDS = [
    "Ace", "Air", "Art", "Bay", "Bee", "Blue", "Bold", "Boom", "Boss", "Box",
    "Buzz", "Cafe", "Cake", "Call", "Calm", "Camp", "Care", "Cash", "Cat", "City",
    "Code", "Cool", "Core", "Cube", "Cup", "Cut", "Dark", "Data", "Dawn", "Day",
    "Deep", "Desk", "Dev", "Dice", "Dig", "Doc", "Door", "Drop", "Duck", "East",
    "Echo", "Edge", "Epic", "Eye", "Face", "Fair", "Fall", "Fame", "Fast", "Fire",
    "Fish", "Five", "Flag", "Flow", "Folk", "Food", "Form", "Fort", "Four", "Free",
    "Fun", "Game", "Gate", "Gear", "Gift", "Girl", "Give", "Goal", "Gold", "Good",
    "Gray", "Green", "Grid", "Grow", "Hand", "Hard", "Head", "Heat", "Help", "Hero",
    "High", "Hill", "Home", "Hope", "Host", "Hour", "Hunt", "Ice", "Idea", "Iron",
    "Item", "Jazz", "Join", "Joy", "Jump", "Just", "Keep", "Key", "Kind", "King",
    "Lake", "Land", "Last", "Lead", "Leaf", "Left", "Life", "Light", "Line", "Link",
    "List", "Live", "Lock", "Long", "Look", "Loop", "Love", "Luck", "Made", "Magic",
    "Main", "Make", "Mark", "Mars", "Math", "Max", "Mind", "Mine", "Moon", "More",
    "Move", "Music", "Name", "Near", "Need", "News", "Next", "Nice", "Nine", "Node",
    "Note", "Nova", "Now", "Oak", "Ocean", "One", "Open", "Page", "Park", "Part",
    "Path", "Peak", "Pink", "Plan", "Play", "Plus", "Pool", "Port", "Post", "Pure",
    "Quest", "Quick", "Rain", "Read", "Real", "Red", "Rest", "Rich", "Ring", "Rise",
    "Road", "Rock", "Room", "Root", "Rose", "Run", "Safe", "Save", "Sea", "Seek",
    "Self", "Send", "Set", "Ship", "Shop", "Show", "Side", "Sign", "Sky", "Slow",
    "Snow", "Soft", "Song", "Soul", "Star", "Step", "Stop", "Sun", "Sure", "Sweet",
    "Talk", "Team", "Tech", "Tell", "Test", "Text", "Time", "Top", "Tree", "True",
    "Turn", "Two", "Use", "User", "View", "Wall", "War", "Wave", "Way", "Web",
    "West", "Wild", "Win", "Wind", "Wing", "Wire", "Wise", "Wood", "Word", "Work",
    "World", "Year", "Yes", "You", "Zone"
]

def load_welcome_messages():
    """加载欢迎语配置"""
    try:
        if os.path.exists("config/welcome_messages.json"):
            with open("config/welcome_messages.json", "r", encoding="utf-8") as f:
                return json.load(f)
        else:
            # 如果文件不存在，返回默认配置
            default_config = {
                "welcome_messages": {
                    "prefixes": ["你好鸭~", "欢迎欢迎~", "哇！", "嗨~"],
                    "suffixes": ["今天也要开开心心的过啊！", "OvO！", "今天也是幸运的一天！"],
                    "standalone": ["你好鸭~今天也要开开心心的过啊！", "欢迎来到这里！OvO！"],
                    "with_nickname": ["{nick}来啦~欢迎欢迎！", "哇！{nick}！今天也要开开心心的过啊！"]
                },
                "settings": {
                    "use_nickname_probability": 0.4,
                    "use_standalone_probability": 0.3,
                    "use_prefix_suffix_probability": 0.3,
                    "enable_random_welcome": True
                }
            }
            return default_config
    except Exception as e:
        log_message("error", f"加载欢迎语配置失败: {e}")
        return None



def get_random_welcome_message(nickname=None):
    """获取随机欢迎语"""
    config = load_welcome_messages()
    if not config or not config["settings"]["enable_random_welcome"]:
        return f"你好鸭~{nickname}！" if nickname else "你好鸭~"

    messages = config["welcome_messages"]
    settings = config["settings"]

    # 根据概率决定使用哪种类型的欢迎语
    rand = random.random()

    if nickname and rand < settings["use_nickname_probability"]:
        # 使用带昵称的欢迎语
        if messages["with_nickname"]:
            template = random.choice(messages["with_nickname"])
            return template.format(nick=nickname)

    elif rand < settings["use_nickname_probability"] + settings["use_standalone_probability"]:
        # 使用独立的欢迎语
        if messages["standalone"]:
            return random.choice(messages["standalone"])

    else:
        # 使用前缀+后缀组合
        if messages["prefixes"] and messages["suffixes"]:
            prefix = random.choice(messages["prefixes"])
            suffix = random.choice(messages["suffixes"])
            if nickname:
                return f"{prefix}{nickname}！{suffix}"
            else:
                return f"{prefix}{suffix}"

    # 如果所有条件都不满足，返回默认欢迎语
    return f"你好鸭~{nickname}！" if nickname else "你好鸭~"

def should_reconnect():
    """判断是否应该重连"""
    return ENABLE_RECONNECT and reconnect_running

def start_websocket_connection():
    """启动WebSocket连接（主循环）"""
    global ws, BOT_CURRENT_NICKNAME
    
    # 关闭 WebSocket 调试输出
    websocket.enableTrace(False)
    
    # 连接循环
    while should_reconnect():
        try:
            # 每次重连都生成新的机器人信息
            nickname, trip_code = generate_bot_info()
            full_name = f"{nickname}#{trip_code}"
            BOT_CURRENT_NICKNAME = nickname  # 保存当前昵称
            log_message("info", f"使用昵称: {nickname}，识别码: {trip_code}")
            
            # 创建新的主实例
            main_instance = main(room="lounges", name=full_name)
            
            log_message("info", "正在连接到 HackChat...")
            
            # 创建WebSocket应用
            ws = websocket.WebSocketApp(
                "wss://icantofun.click/",
                on_message=main_instance.on_message,
                on_error=main_instance.on_error,
                on_close=main_instance.on_close,
                on_open=main_instance.on_open
            )

            # 启动连接（这会阻塞直到连接关闭）
            ws.run_forever()

        except Exception as e:
            log_message("error", f"WebSocket连接失败: {e}")

        # 如果需要重连，等待1秒后继续循环
        if should_reconnect():
            log_message("info", "连接断开，1秒后重连...")
            time.sleep(1)
        else:
            log_message("info", "停止重连")
            break

def initialize_directories_and_files():
    """初始化必要的目录和文件"""
    # 创建必要的目录
    directories = ['history', 'userdata', 'userdata/ai', 'temp', 'config', 'data/games', 'fonts']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        log_message("info", f"确保目录存在: {directory}")

    # 创建必要的文件
    files_to_create = {
        'config/messages.json': '{}'
    }

    for filename, default_content in files_to_create.items():
        if not os.path.exists(filename):
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(default_content)
                log_message("info", f"创建文件: {filename}")
            except Exception as e:
                log_message("error", f"创建文件失败 {filename}: {e}")
        else:
            log_message("info", f"文件已存在: {filename}")

    log_message("info", "初始化完成！")



class runbox:
    def __init__(self, room, name):
        self.room = room
        self.name = name
        self.online_users = []
        self.game_on = False
        self.secret_number = None
        self.ad_users = []

    def handle(self, json_data):
        self.json_data = json_data
        if "cmd" in self.json_data:
            if self.json_data["cmd"] == "chat":
                # 记录聊天消息
                nick = self.json_data.get("nick", "unknown")
                text = self.json_data.get("text", "")
                log_message("chat", text, nick)
                self.chat()
            elif self.json_data["cmd"] == "updateMessage":
                # 处理消息更新 - 这是 HackChat upstream 处理的最终结果
                self.handle_message_update()
            elif self.json_data["cmd"] == "info":
                # 记录系统信息
                info_text = self.json_data.get("text", "")
                if "whispered" in info_text:
                    log_message("whisper", info_text)
                else:
                    log_message("info", info_text)
                self.info()
            elif self.json_data["cmd"] == "onlineAdd":
                # 记录用户加入
                self.nick = self.json_data['nick']
                log_message("join", f"用户加入房间", self.nick)
                self.onlineadd()
            elif self.json_data["cmd"] == "onlineRemove":
                # 记录用户离开
                nick = self.json_data.get('nick', 'unknown')
                log_message("leave", f"用户离开房间", nick)
            else:
                log_message("error", f"未知命令: {self.json_data.get('cmd', 'unknown')}")

    def handle_command(self, command_type, username, get, custom_id):
        try:
            # 使用统一的 AI 处理脚本
            output = subprocess.check_output(['python', 'modules/ai.py', command_type, username, get], universal_newlines=True)
        except subprocess.CalledProcessError as e:
            output = f"命令执行失败: {str(e)}"
        except Exception as e:
            output = f"未知错误: {str(e)}"

        response = username + "，" + output
        log_message("bot_send", response)
        ws.send(update(response, custom_id))

    def handle_message_update(self):
        """处理 HackChat 的 updateMessage 命令 - 更新历史记录中的消息"""
        try:
            custom_id = self.json_data.get("customId")
            updated_text = self.json_data.get("text", "")
            nick = self.json_data.get("nick", "unknown")

            if not custom_id:
                return

            log_message("info", f"收到消息更新: {custom_id} -> {updated_text[:50]}...")

            # 更新历史记录文件中的对应消息
            self.update_message_in_history(custom_id, updated_text, nick)

        except Exception as e:
            log_message("error", f"处理消息更新失败: {e}")

    def update_message_in_history(self, custom_id, new_text, nick):
        """在历史记录文件中更新指定 customId 的消息"""
        try:
            # 确保 history 目录存在
            history_dir = "history"
            os.makedirs(history_dir, exist_ok=True)

            # 使用更安全的文件名格式
            date_str = time.strftime("%Y-%m-%d")
            room_safe = self.room.replace(" ", "_").replace("/", "_")
            filename = f"{date_str}_{room_safe}.jsonl"
            filepath = os.path.join(history_dir, filename)

            if not os.path.exists(filepath):
                return

            # 读取所有消息
            messages = []
            with open(filepath, "r", encoding="utf-8") as fp:
                for line in fp:
                    if line.strip():
                        try:
                            msg = json.loads(line.strip())
                            messages.append(msg)
                        except json.JSONDecodeError:
                            continue

            # 查找并更新对应的消息
            updated = False
            for msg in messages:
                if msg.get("custom_id") == custom_id:
                    msg["text"] = new_text
                    msg["updated_at"] = time.strftime("%Y-%m-%d %H:%M:%S")
                    updated = True
                    log_message("info", f"已更新历史记录中的消息: {custom_id}")
                    break

            if updated:
                # 重写整个文件
                with open(filepath, "w", encoding="utf-8") as fp:
                    for msg in messages:
                        fp.write(json.dumps(msg, ensure_ascii=False) + "\n")

        except Exception as e:
            log_message("error", f"更新历史记录失败: {e}")

    def chat(self):
        # 记录聊天历史 - 改进版
        self.log_chat_message()

    def log_chat_message(self):
        """改进的聊天记录功能"""
        try:
            # 确保 history 目录存在
            history_dir = "history"
            os.makedirs(history_dir, exist_ok=True)

            # 使用更安全的文件名格式
            date_str = time.strftime("%Y-%m-%d")
            room_safe = self.room.replace(" ", "_").replace("/", "_")
            filename = f"{date_str}_{room_safe}.jsonl"
            filepath = os.path.join(history_dir, filename)

            # 创建结构化的消息记录
            message_record = {
                "timestamp": time.time(),
                "datetime": time.strftime("%Y-%m-%d %H:%M:%S"),
                "room": self.room,
                "nick": self.json_data.get("nick", "unknown"),
                "trip": self.json_data.get("trip", ""),
                "text": self.json_data.get("text", ""),
                "cmd": self.json_data.get("cmd", "chat")
            }

            # 如果消息有 customId，也保存它以便后续更新
            if "customId" in self.json_data:
                message_record["custom_id"] = self.json_data["customId"]

            # 使用UTF-8编码安全写入（跨平台兼容）
            with open(filepath, "a", encoding="utf-8") as fp:
                try:
                    # 尝试使用文件锁（Unix/Linux）
                    import fcntl
                    fcntl.flock(fp.fileno(), fcntl.LOCK_EX)
                    fp.write(json.dumps(message_record, ensure_ascii=False) + "\n")
                    fcntl.flock(fp.fileno(), fcntl.LOCK_UN)
                except ImportError:
                    # Windows 系统不支持 fcntl，直接写入
                    fp.write(json.dumps(message_record, ensure_ascii=False) + "\n")

        except Exception as e:
            print(f"记录聊天历史失败: {e}")  # 记录错误但不影响主功能

        # 定义命令配置
        commands = {
            "draw": {"script": "draw", "prefix_len": 5, "message": "正在使用 Flux 模型绘画，请稍后..."}
        }

        # 检查是否是机器人自己的消息（昵称以 BoB 开头）
        def is_bot_message(nick):
            return nick.startswith(BOT_BASE_NAME)

        # 处理留言命令
        if self.json_data["text"].startswith("msg "):
            parts = self.json_data["text"].split(" ", 2)
            if len(parts) >= 3:
                to_user = parts[1]
                message = parts[2]
                self.leave_message(to_user, message)
                return

        # 处理 UNO 游戏命令
        if self.json_data["text"].startswith("uno "):
            self.handle_uno_command()
            return

        # 处理 Wordle 游戏命令
        if self.json_data["text"].startswith("wordle "):
            self.handle_wordle_command()
            return

        # 处理 Wordle 猜测（仅5个英文字母）
        text = self.json_data["text"].strip()
        if len(text) == 5 and text.isalpha() and text.isascii() and wordle_game.game_active:
            self.handle_wordle_guess(text)
            return

        # 检查@BoB或@BoB_XXXX命令（支持双重调用方式）
        bot_nick_for_mention = self.name.split('#')[0] if '#' in self.name else self.name
        text_content = self.json_data["text"]
        
        # 检查是否是@BoB或@BoB_xxxx的调用
        is_bot_mention = False
        ai_content = ""
        
        if text_content.startswith(f"@{bot_nick_for_mention} ") and not is_bot_message(self.json_data["nick"]):
            # @BoB_xxxx调用
            is_bot_mention = True
            ai_content = text_content[len(f"@{bot_nick_for_mention} "):]
        elif text_content.startswith(f"@{BOT_BASE_NAME} ") and not is_bot_message(self.json_data["nick"]):
            # @BoB调用
            is_bot_mention = True
            ai_content = text_content[len(f"@{BOT_BASE_NAME} "):]
        
        if is_bot_mention:
            username = self.json_data["nick"]
            custom_id = str(random.randint(100000, 999999))
            ws.send(sendd(username + "，正在分析并生成回答，请稍后...", custom_id))

            # 创建新线程处理命令
            thread = threading.Thread(target=self.handle_command, args=("ai", username, ai_content, custom_id))
            thread.start()
            return

        # 统一处理其他命令
        for command, config in commands.items():
            if self.json_data["text"].startswith(command + " ") and not is_bot_message(self.json_data["nick"]):
                username = self.json_data["nick"]
                get = self.json_data["text"][config["prefix_len"]:]
                custom_id = str(random.randint(100000, 999999))
                ws.send(sendd(username + "，" + config["message"], custom_id))

                # 创建新线程处理命令
                thread = threading.Thread(target=self.handle_command, args=(config["script"], username, get, custom_id))
                thread.start()
                return  # 找到匹配的命令后直接返回

        if "delete" == self.json_data["text"]:
            # 确保 userdata/ai 目录存在
            ai_dir = "userdata/ai"
            os.makedirs(ai_dir, exist_ok=True)

            filename = self.json_data["nick"] + "_chat_history.json"
            filepath = os.path.join(ai_dir, filename)
            if os.path.exists(filepath):
                os.remove(filepath)
                ws.send(sendd("聊天记录已删除"))
            else:
                ws.send(sendd("找不到聊天记录文件"))

        # history command - 改进版
        elif (self.json_data["text"] == "history" or
              (self.json_data["text"].startswith("history ") and
               len(self.json_data["text"].split()) == 2 and
               self.json_data["text"].split()[1].isdigit())):
            self.show_chat_history()

        # 基础help指令
        elif self.json_data["text"] == "help":
            self.handle_main_help()

        # 简化帮助系统 - 直接输入指令名获取帮助（优先处理）
        elif self.json_data["text"].strip() in ["uno", "wordle", "idiom", "shop", "rank", "ai", "stock", "checkin", "inventory", "all"]:
            self.handle_simple_help_command()

        elif self.json_data["text"] == "coins":
            # 直接查看金币数量
            nick = self.json_data["nick"]
            trip = self.json_data.get("trip", "")
            coins = user_system.get_user_coins(nick, trip)
            ws.send(sendd(f"{nick} 当前拥有 {coins} 金币"))



        elif self.json_data["text"].startswith("shop "):
            self.handle_shop_command()

        # 简化股票指令
        elif self.json_data["text"].startswith("get ") or self.json_data["text"].startswith("sell ") or self.json_data["text"] == "portfolio" or self.json_data["text"].startswith("price "):
            self.handle_simplified_stock_command()

        elif self.json_data["text"].startswith("buy "):
            self.handle_buy_command()

        elif self.json_data["text"].startswith("rank "):
            self.handle_rank_command()
        
        elif self.json_data["text"].startswith("stock "):
            self.handle_stock_command()

        elif self.json_data["text"].startswith("idiom "):
            self.handle_idiom_command()

        elif self.json_data["text"].startswith("ping") or self.json_data["text"] == "签到":
            self.handle_checkin_command()

        elif self.json_data["text"].startswith("bag") or self.json_data["text"] == "背包":
            self.handle_inventory_command()

        elif self.json_data["text"] == "skip" and idiom_game.game_started:
            self.handle_skip_card_command()

        elif self.json_data["text"] == "hint" and wordle_game.game_active:
            self.handle_hint_card_command()

        elif self.json_data["text"].startswith("wel"):
            self.handle_welcome_command()

        # 检查是否是成语接龙猜测（4个中文字符）
        elif (len(self.json_data["text"]) == 4 and
              all('\u4e00' <= char <= '\u9fff' for char in self.json_data["text"])):
            self.handle_idiom_guess(self.json_data["text"])

    def show_chat_history(self):
        """改进的历史记录显示功能"""
        try:
            args = self.json_data["text"].split()
            # 解析请求的消息数量
            if len(args) > 1 and args[1].isdigit():
                requested_count = int(args[1])
                requested_count = min(requested_count, 500)  # 限制最大数量
            else:
                requested_count = None  # 使用默认数量

            # 确保 history 目录存在
            history_dir = "history"
            os.makedirs(history_dir, exist_ok=True)

            date_str = time.strftime("%Y-%m-%d")
            room_safe = self.room.replace(" ", "_").replace("/", "_")
            filename = f"{date_str}_{room_safe}.jsonl"
            filepath = os.path.join(history_dir, filename)

            messages = []

            # 读取 JSON Lines 格式的历史记录
            try:
                with open(filepath, "r", encoding="utf-8") as fp:
                    for line in fp:
                        if line.strip():
                            try:
                                msg = json.loads(line.strip())
                                # 只显示聊天消息，过滤系统消息
                                if msg.get("cmd") == "chat" and msg.get("text"):
                                    messages.append(msg)
                            except json.JSONDecodeError:
                                continue
            except FileNotFoundError:
                pass

            if not messages:
                ws.send(sendd("/w " + self.json_data["nick"] + " 今天还没有聊天记录"))
                return

            # 格式化消息用于 HTML 显示
            formatted_messages = []
            for msg in messages:
                formatted_msg = {
                    "time": msg.get("datetime", "").split()[1] if " " in msg.get("datetime", "") else "",
                    "nick": msg.get("nick", "unknown"),
                    "text": msg.get("text", "")
                }
                formatted_messages.append(formatted_msg)

            # 使用 HTML 生成器处理请求
            result_type, content, message_count = process_history_request(
                formatted_messages, self.room, date_str, requested_count
            )

            if result_type == "file":
                # HTML 文件上传成功
                ws.send(sendd(f"/w {self.json_data['nick']} 📄 历史记录已生成 HTML 文件: {content}\n包含最近 {message_count} 条消息"))
            elif result_type == "text":
                # 直接文本显示
                ws.send(sendd(f"/w {self.json_data['nick']} 最近 {message_count} 条消息:\n{content}"))
            elif result_type == "text_fallback":
                # 上传失败，分块发送
                max_length = 1000
                chunks = [content[i:i+max_length] for i in range(0, len(content), max_length)]
                for i, chunk in enumerate(chunks):
                    ws.send(sendd(f"/w {self.json_data['nick']} 历史记录 ({i+1}/{len(chunks)}):\n{chunk}"))
                ws.send(sendd(f"/w {self.json_data['nick']} ⚠️ HTML 文件上传失败，已分块显示"))

        except Exception as e:
            ws.send(sendd(f"/w {self.json_data['nick']} 获取历史记录失败: {str(e)}"))




    def info(self):
        if "whispered" in self.json_data["text"]:
            username = self.json_data["from"]
            parts = self.json_data["text"].split("whispered:", 1)
            if len(parts) > 1:
                get = parts[1].strip()
                try:
                    output = subprocess.check_output(['python', 'ai.py', 'ai', username, get], universal_newlines=True)
                    ws.send(sendd(f'/w {username} ' + output))
                except subprocess.CalledProcessError as e:
                    ws.send(sendd(f'/w {username} AI 执行出错: {str(e)}'))
                except Exception as e:
                    ws.send(sendd(f'/w {username} 未知错误: {str(e)}'))



    def onlineadd(self):
        # 使用全局用户系统实例
        global user_system

        # 获取当前进入房间的用户的昵称
        user = self.nick

        # 检查用户是否有留言
        self.check_messages(user)

        # 获取用户trip信息
        trip = ""
        if hasattr(self, 'json_data') and self.json_data:
            trip = self.json_data.get("trip", "")

        # 检查用户是否有自定义欢迎语
        user_key = user_system.get_user_key(user, trip)
        if user_key in user_system.user_data["users"]:
            user_data = user_system.user_data["users"][user_key]
            if (user_data.get("custom_welcome") and
                user_data.get("custom_welcome_enabled", False)):
                # 用户有启用的自定义欢迎语，在公开聊天中显示
                custom_welcome = user_data["custom_welcome"]
                log_message("bot_send", f"自定义欢迎语: {custom_welcome}")
                ws.send(sendd(custom_welcome))
            else:
                # 使用默认随机欢迎语
                random_welcome = get_random_welcome_message(user)
                log_message("bot_send", random_welcome)
                ws.send(sendd(random_welcome))
        else:
            # 新用户，使用默认随机欢迎语
            random_welcome = get_random_welcome_message(user)
            log_message("bot_send", random_welcome)
            ws.send(sendd(random_welcome))

        # 检查是否为新用户，如果是则发送私聊介绍
        if not user_system.is_known_user(user):
            # 新用户 - 额外发送私聊欢迎消息
            welcome_msg = user_system.get_user_welcome_message(user, trip)
            whisper_msg = f"/w {user} {welcome_msg}"

            log_message("bot_send", f"私聊新用户: {welcome_msg}")
            ws.send(sendd(whisper_msg))

            # 添加到已知用户列表
            user_system.add_user(user)





    def leave_message(self, to_user, message):
        """留言功能 - JSON 版本"""
        try:
            # 留言文件路径
            message_file = "config/messages.json"

            # 加载现有留言
            messages_data = {}
            if os.path.exists(message_file):
                try:
                    with open(message_file, "r", encoding="utf-8") as f:
                        messages_data = json.load(f)
                except json.JSONDecodeError:
                    # 文件损坏，创建新文件
                    messages_data = {}

            # 确保用户的留言列表存在
            if to_user not in messages_data:
                messages_data[to_user] = []

            # 创建留言记录
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            new_message = {
                "timestamp": timestamp,
                "from": self.json_data.get('nick', 'unknown'),
                "message": message
            }

            # 添加新留言
            messages_data[to_user].append(new_message)

            # 保存留言
            with open(message_file, "w", encoding="utf-8") as f:
                json.dump(messages_data, f, ensure_ascii=False, indent=2)

            ws.send(sendd(f"已给 {to_user} 留言成功！当 {to_user} 进入房间时会收到您的消息。"))

        except Exception as e:
            ws.send(sendd(f"留言失败: {str(e)}"))

    def check_messages(self, username):
        """检查用户是否有留言 - JSON 版本"""
        try:
            message_file = "config/messages.json"
            if not os.path.exists(message_file):
                return

            # 加载留言数据
            with open(message_file, "r", encoding="utf-8") as f:
                try:
                    messages_data = json.load(f)
                except json.JSONDecodeError:
                    return

            # 检查用户是否有留言
            if username in messages_data and messages_data[username]:
                # 格式化留言
                formatted_messages = []
                for msg in messages_data[username]:
                    formatted_messages.append(
                        f"[{msg['timestamp']}] {msg['from']}: {msg['message']}"
                    )

                # 发送留言给用户
                message_text = "\n".join(formatted_messages)
                ws.send(sendd(f"/w {username} 您有新留言:\n{message_text}"))

                # 删除已读留言
                messages_data.pop(username)

                # 保存更新后的留言数据
                with open(message_file, "w", encoding="utf-8") as f:
                    json.dump(messages_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"检查留言失败: {e}")



    def handle_shop_command(self):
        """处理商店相关命令"""
        text = self.json_data["text"]
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        parts = text.split()
        if len(parts) == 1:
            # 这种情况不应该出现，因为单独shop会走帮助系统
            ws.send(sendd("请使用 'shop list' 查看商店物品"))
            return
        
        command = parts[1]
        
        if command == "list":
            # 显示商店物品
            shop_items = user_system.get_shop_items()
            shop_text = "🛒 商店物品:\n"
            for item_id, item in shop_items.items():
                shop_text += f"[{item_id}] {item['name']} - {item['price']} 金币\n"
                shop_text += f"    {item['description']}\n"
                if 'update_price' in item:
                    shop_text += f"    更新价格: {item['update_price']} 金币\n"
                shop_text += "\n"
            shop_text += "使用 'buy <商品号>' 购买物品\n"
            shop_text += "例如: buy 1 你的欢迎语"
            ws.send(sendd(shop_text))

        elif command == "buy" and len(parts) >= 3:
            item_id = parts[2]

            # 支持数字购买
            if item_id.isdigit():
                args = parts[3:] if len(parts) > 3 else []
                success, message, order_id = user_system.buy_item_by_id(nick, trip, item_id, *args)
                ws.send(sendd(f"{nick}: {message}"))

            # 兼容旧的文字购买方式
            elif item_id == "welcome" or item_id == "欢迎语":
                if len(parts) < 4:
                    ws.send(sendd("请提供欢迎语内容，格式: shop buy 1 你的欢迎语"))
                    return
                welcome_message = " ".join(parts[3:])
                success, message, order_id = user_system.buy_custom_welcome(nick, welcome_message, trip)
                ws.send(sendd(f"{nick}: {message}"))
            else:
                ws.send(sendd("请使用商品编号购买，例如: shop buy 1"))

        elif command == "update" and len(parts) >= 3:
            item_name = parts[2]
            if item_name == "welcome" or item_name == "欢迎语":
                if len(parts) < 4:
                    ws.send(sendd("请提供新的欢迎语内容，格式: shop update welcome 新的欢迎语"))
                    return
                welcome_message = " ".join(parts[3:])
                success, message = user_system.update_custom_welcome(nick, welcome_message, trip)
                ws.send(sendd(f"{nick}: {message}"))
            else:
                ws.send(sendd("未知的商品"))

        elif command == "toggle" and len(parts) >= 3:
            item_name = parts[2]
            if item_name == "welcome" or item_name == "欢迎语":
                if len(parts) >= 4:
                    status = parts[3].lower()
                    enabled = status in ["on", "开启", "true", "1"]
                    success, message = user_system.toggle_custom_welcome(nick, enabled, trip)
                    ws.send(sendd(f"{nick}: {message}"))
                else:
                    ws.send(sendd("请指定开启或关闭，格式: shop toggle welcome on/off"))
            else:
                ws.send(sendd("未知的商品"))

        elif command == "history":
            # 查看购买历史
            history = user_system.get_purchase_history(nick, trip)
            if history:
                history_text = "🛒 购买历史:\n"
                for record in history[-5:]:  # 显示最近5条
                    history_text += f"• {record['datetime']} - {record['item_name']}\n"
                    history_text += f"  流水号: {record['order_id']} | 花费: {record['price']}金币\n"
                ws.send(sendd(history_text))
            else:
                ws.send(sendd("暂无购买记录"))

        else:
            ws.send(sendd("未知的商店命令，使用 'shop' 查看帮助"))

    def handle_buy_command(self):
        """处理购买命令"""
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")
        text = self.json_data["text"]

        # 解析buy命令
        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd(f"{nick}: 请指定要购买的物品ID，例如: buy 1"))
            return

        item_id = parts[1]
        args = parts[2:] if len(parts) > 2 else []

        # 执行购买
        success, message, order_id = user_system.buy_item_by_id(nick, trip, item_id, *args)

        if success:
            ws.send(sendd(f"✅ {nick}: {message}"))
        else:
            ws.send(sendd(f"❌ {nick}: {message}"))

    def handle_rank_command(self):
        """处理排名相关命令"""
        text = self.json_data["text"]
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        parts = text.split()
        if len(parts) == 1:
            # 这种情况不应该出现，因为单独rank会走帮助系统
            ws.send(sendd("请指定排行榜类型:\n`rank list` - 显示使用提示\n`rank 1` - 金币排行榜\n`rank 2` - Wordle计时排行榜\n`rank 3` - 成语接龙排行榜"))
            return
        
        command = parts[1]
        
        if command == "list":
            # 显示使用提示
            ws.send(sendd("请指定排行榜类型:\n`rank 1` - 金币排行榜\n`rank 2` - Wordle计时排行榜\n`rank 3` - 成语接龙排行榜\n`rank <用户名>` - 查看用户排名"))
            return

        param = parts[1]

        if param == "1":
            # 金币排行榜
            rankings = user_system.get_coin_rankings(10)
            if not rankings:
                ws.send(sendd("暂无金币排行榜数据"))
                return

            rank_text = "🏆 金币排行榜 (前10名):\n"
            for i, (username, coins) in enumerate(rankings, 1):
                if i == 1:
                    rank_text += f"🥇 {i}. {username} - {coins} 金币\n"
                elif i == 2:
                    rank_text += f"🥈 {i}. {username} - {coins} 金币\n"
                elif i == 3:
                    rank_text += f"🥉 {i}. {username} - {coins} 金币\n"
                else:
                    rank_text += f"   {i}. {username} - {coins} 金币\n"

            ws.send(sendd(rank_text.strip()))

        elif param == "2":
            # Wordle计时排行榜
            rankings = user_system.get_wordle_rankings(10)
            if not rankings:
                ws.send(sendd("暂无Wordle计时排行榜数据"))
                return

            rank_text = "⏱️ Wordle计时排行榜 (前10名):\n"
            for i, (username, time_seconds) in enumerate(rankings, 1):
                minutes = time_seconds // 60
                seconds = time_seconds % 60
                time_str = f"{minutes}分{seconds}秒" if minutes > 0 else f"{seconds}秒"

                if i == 1:
                    rank_text += f"🥇 {i}. {username} - {time_str}\n"
                elif i == 2:
                    rank_text += f"🥈 {i}. {username} - {time_str}\n"
                elif i == 3:
                    rank_text += f"🥉 {i}. {username} - {time_str}\n"
                else:
                    rank_text += f"   {i}. {username} - {time_str}\n"

            ws.send(sendd(rank_text.strip()))

        elif param == "3":
            # 成语接龙轮次排行榜
            rankings = user_system.get_idiom_rankings(10)
            if not rankings:
                ws.send(sendd("暂无成语接龙排行榜数据"))
                return

            rank_text = "🀄 成语接龙排行榜 (前10名):\n"
            for i, (username, rounds) in enumerate(rankings, 1):
                if i == 1:
                    rank_text += f"🥇 {i}. {username} - {rounds} 轮\n"
                elif i == 2:
                    rank_text += f"🥈 {i}. {username} - {rounds} 轮\n"
                elif i == 3:
                    rank_text += f"🥉 {i}. {username} - {rounds} 轮\n"
                else:
                    rank_text += f"   {i}. {username} - {rounds} 轮\n"

            ws.send(sendd(rank_text.strip()))

        else:
            # 查看指定用户的排名
            target_user = param
            rank, coins = user_system.get_user_rank(target_user)

            if rank is None:
                ws.send(sendd(f"用户 {target_user} 暂无金币记录"))
            else:
                ws.send(sendd(f"{target_user} 的排名: 第 {rank} 名，拥有 {coins} 金币"))

    def handle_stock_command(self):
        """处理股票交易命令"""
        text = self.json_data["text"]
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")
        
        parts = text.split()
        if len(parts) == 1:
            # 显示帮助信息
            help_text = "📈 **股票交易系统**\n\n"
            help_text += "**基本命令**:\n"
            help_text += "`stock list` - 查看所有可交易股票\n"
            help_text += "`stock get <股票代码> <数量>` - 买入股票\n"
            help_text += "`stock sell <股票代码> <数量>` - 卖出股票\n"
            help_text += "`stock portfolio` - 查看投资组合\n"
            help_text += "`stock price <股票代码>` - 查看股票详情\n\n"
            help_text += "**说明**:\n"
            help_text += "• 使用虚拟金币进行交易\n"
            help_text += "• 股价基于真实公司但为模拟数据\n"
            help_text += "• 包含中美知名公司股票\n"
            help_text += "• 价格每30秒更新一次"
            ws.send(sendd(f"/w {nick} {help_text}"))
            return
        
        command = parts[1].lower()
        
        if command == "list":
            # 显示股票列表
            stock_list = stock_game.get_stock_list()
            ws.send(sendd(f"/w {nick} {stock_list}"))
        
        elif command == "get" and len(parts) >= 4:
            # 买入股票
            symbol = parts[2]
            try:
                quantity = int(parts[3])
                if quantity <= 0:
                    ws.send(sendd(f"{nick}: 购买数量必须大于0"))
                    return
                
                user_coins = user_system.get_user_coins(nick, trip)
                success, message, cost = stock_game.buy_stock(user_coins, symbol, quantity)
                
                if success:
                    # 执行购买
                    if user_system.buy_stock(nick, trip, symbol, quantity, cost):
                        new_total = user_system.get_user_coins(nick, trip)
                        ws.send(sendd(f"✅ {nick}: {message}\n💰 剩余金币: {new_total}"))
                    else:
                        ws.send(sendd(f"❌ {nick}: 购买失败，请重试"))
                else:
                    ws.send(sendd(f"❌ {nick}: {message}"))
                    
            except ValueError:
                ws.send(sendd(f"{nick}: 请输入有效的购买数量"))
        
        elif command == "sell" and len(parts) >= 4:
            # 卖出股票
            symbol = parts[2].upper()
            try:
                quantity = int(parts[3])
                if quantity <= 0:
                    ws.send(sendd(f"{nick}: 卖出数量必须大于0"))
                    return
                
                portfolio = user_system.get_user_portfolio(nick, trip)
                success, message, revenue = stock_game.sell_stock(portfolio, symbol, quantity)
                
                if success:
                    # 执行卖出
                    if user_system.sell_stock(nick, trip, symbol, quantity, revenue):
                        new_total = user_system.get_user_coins(nick, trip)
                        ws.send(sendd(f"✅ {nick}: {message}\n💰 当前金币: {new_total}"))
                    else:
                        ws.send(sendd(f"❌ {nick}: 卖出失败，请重试"))
                else:
                    ws.send(sendd(f"❌ {nick}: {message}"))
                    
            except ValueError:
                ws.send(sendd(f"{nick}: 请输入有效的卖出数量"))
        
        elif command == "portfolio":
            # 查看投资组合
            portfolio = user_system.get_user_portfolio(nick, trip)
            total_value, portfolio_text = stock_game.get_portfolio_value(portfolio)
            
            user_coins = user_system.get_user_coins(nick, trip)
            total_assets = user_coins + total_value
            
            portfolio_text += f"\n💰 **现金**: {user_coins} 金币"
            portfolio_text += f"\n💎 **总资产**: {total_assets} 金币"
            
            ws.send(sendd(f"/w {nick} {portfolio_text}"))
        
        elif command == "price" and len(parts) >= 3:
            # 查看股票详情
            symbol = parts[2].upper()
            detail = stock_game.get_stock_detail(symbol)
            
            if detail:
                ws.send(sendd(f"/w {nick} {detail}"))
            else:
                ws.send(sendd(f"{nick}: 股票代码 {symbol} 不存在"))
        
        else:
            ws.send(sendd(f"{nick}: 无效的股票命令，使用 `stock` 查看帮助"))

    def handle_simplified_stock_command(self):
        """处理简化的股票指令"""
        text = self.json_data["text"]
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")
        
        parts = text.split()
        command = parts[0].lower()
        
        if command == "get" and len(parts) >= 3:
            # 买入股票：get <代码> <数量>
            symbol = parts[1]
            try:
                quantity = int(parts[2])
                if quantity <= 0:
                    ws.send(sendd(f"{nick}: 购买数量必须大于0"))
                    return
                
                user_coins = user_system.get_user_coins(nick, trip)
                success, message, cost = stock_game.buy_stock(user_coins, symbol, quantity)
                
                if success:
                    if user_system.buy_stock(nick, trip, symbol, quantity, cost):
                        new_total = user_system.get_user_coins(nick, trip)
                        ws.send(sendd(f"✅ {nick}: {message}\n💰 剩余金币: {new_total}"))
                    else:
                        ws.send(sendd(f"❌ {nick}: 购买失败，请重试"))
                else:
                    ws.send(sendd(f"❌ {nick}: {message}"))
                    
            except ValueError:
                ws.send(sendd(f"{nick}: 请输入有效的购买数量"))
        
        elif command == "sell" and len(parts) >= 3:
            # 卖出股票：sell <代码> <数量>
            symbol = parts[1]
            try:
                quantity = int(parts[2])
                if quantity <= 0:
                    ws.send(sendd(f"{nick}: 卖出数量必须大于0"))
                    return
                
                portfolio = user_system.get_user_portfolio(nick, trip)
                success, message, revenue = stock_game.sell_stock(portfolio, symbol, quantity)
                
                if success:
                    if user_system.sell_stock(nick, trip, symbol, quantity, revenue):
                        new_total = user_system.get_user_coins(nick, trip)
                        ws.send(sendd(f"✅ {nick}: {message}\n💰 当前金币: {new_total}"))
                    else:
                        ws.send(sendd(f"❌ {nick}: 卖出失败，请重试"))
                else:
                    ws.send(sendd(f"❌ {nick}: {message}"))
                    
            except ValueError:
                ws.send(sendd(f"{nick}: 请输入有效的卖出数量"))
        
        elif command == "portfolio":
            # 查看投资组合
            portfolio = user_system.get_user_portfolio(nick, trip)
            total_value, portfolio_text = stock_game.get_portfolio_value(portfolio)
            
            user_coins = user_system.get_user_coins(nick, trip)
            total_assets = user_coins + total_value
            
            portfolio_text += f"\n💰 **现金**: {user_coins} 金币"
            portfolio_text += f"\n💎 **总资产**: {total_assets} 金币"
            
            ws.send(sendd(f"/w {nick} {portfolio_text}"))
        
        elif command == "price" and len(parts) >= 2:
            # 查看股票详情：price <代码>
            symbol = parts[1]
            detail = stock_game.get_stock_detail(symbol)
            
            if detail:
                ws.send(sendd(f"/w {nick} {detail}"))
            else:
                ws.send(sendd(f"{nick}: 股票代码 {symbol} 不存在"))
        
        else:
            ws.send(sendd(f"{nick}: 指令格式错误。使用 `stock` 查看帮助"))

    def handle_idiom_command(self):
        """处理成语接龙游戏命令"""
        global idiom_game

        text = self.json_data["text"]
        nick = self.json_data["nick"]

        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd("成语接龙命令格式错误"))
            return

        command = parts[1]

        if command == "join":
            # 加入游戏
            success, message = idiom_game.add_player(nick)
            ws.send(sendd(message))

        elif command == "leave":
            # 离开游戏
            success, message = idiom_game.remove_player(nick)
            ws.send(sendd(message))
            if success and idiom_game.game_started and len([p for p in idiom_game.players if p not in idiom_game.eliminated_players]) < 2:
                idiom_game.reset_game()
                ws.send(sendd("人数不足，游戏重置"))

        elif command == "start":
            # 开始游戏
            success, message = idiom_game.start_game()
            ws.send(sendd(message))
            if success:
                # 设置回调函数
                idiom_game.on_player_timeout = self.on_idiom_timeout
                idiom_game.on_game_end = self.on_idiom_game_end

        elif command == "status":
            # 查看游戏状态
            ws.send(sendd(idiom_game.get_game_status()))

        elif command == "reset":
            # 重置游戏
            idiom_game.reset_game()
            ws.send(sendd("成语接龙游戏已重置"))

        else:
            ws.send(sendd("未知的成语接龙命令"))

    def on_idiom_timeout(self, player_name):
        """成语接龙超时回调"""
        ws.send(sendd(f"⏰ {player_name} 超时被淘汰！"))

        # 检查游戏是否结束
        if idiom_game._check_game_end():
            self.on_idiom_game_end()
        else:
            # 继续下一轮
            next_player = idiom_game.get_current_player()
            if next_player:
                ws.send(sendd(f"现在轮到 {next_player}，请接'{idiom_game.current_last_pinyin}'音"))

    def on_idiom_game_end(self):
        """成语接龙游戏结束回调"""
        rankings = idiom_game.get_rankings()

        # 记录每个玩家的轮次数（已使用成语数量）
        rounds_count = len(idiom_game.used_idioms)

        # 发放金币奖励
        if len(rankings) >= 3:
            # 只有3人或以上才发放金币
            rewards = {1: 100, 2: 60, 3: 30}  # 第一名100金币，第二名60金币，第三名30金币

            reward_messages = []
            for i, player in enumerate(rankings[:3], 1):
                if i in rewards:
                    # 这里需要获取玩家的trip，但在当前架构下比较困难
                    # 暂时使用空trip，后续可以改进
                    coins_earned = rewards[i]
                    new_total = user_system.add_coins(player, coins_earned, "")

                    # 记录成语接龙轮次（只为获胜者记录）
                    if i == 1:
                        is_record, max_rounds = user_system.record_idiom_rounds(player, "", rounds_count)
                        if is_record:
                            reward_messages.append(f"🏆 第{i}名 {player}: +{coins_earned}金币 (总计: {new_total}) 🆕新纪录: {rounds_count}轮!")
                        else:
                            reward_messages.append(f"🏆 第{i}名 {player}: +{coins_earned}金币 (总计: {new_total})")
                    else:
                        reward_messages.append(f"🏆 第{i}名 {player}: +{coins_earned}金币 (总计: {new_total})")

            ws.send(sendd("🎉 成语接龙游戏结束！\n" + "\n".join(reward_messages)))
        else:
            ws.send(sendd("🎉 成语接龙游戏结束！参与人数不足3人，无金币奖励"))
        
        # 重置游戏状态，允许新玩家加入
        idiom_game.reset_game()

    def handle_uno_command(self):
        """处理 UNO 游戏命令"""
        global uno_game

        text = self.json_data["text"]
        nick = self.json_data["nick"]

        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd("UNO命令格式错误"))
            return

        command = parts[1]

        if command == "join":
            # 加入游戏
            if uno_game.add_player(nick):
                ws.send(sendd(f"{nick} 加入了UNO游戏！当前玩家: {len(uno_game.players)}人"))
            else:
                ws.send(sendd("加入失败，可能已在游戏中或游戏已满"))

        elif command == "leave":
            # 离开游戏
            if uno_game.remove_player(nick):
                ws.send(sendd(f"{nick} 离开了UNO游戏"))
                if uno_game.game_started and len(uno_game.players) < 2:
                    uno_game = UnoGame()  # 重置游戏
                    ws.send(sendd("人数不足，游戏重置"))
            else:
                ws.send(sendd("你不在游戏中"))

        elif command == "start":
            # 开始游戏
            if uno_game.start_game():
                ws.send(sendd("🎮 UNO游戏开始！"))
                ws.send(sendd(uno_game.get_game_status()))
                # 私聊发送每个玩家的手牌
                for player in uno_game.players:
                    hand = uno_game.get_player_hand(player)
                    ws.send(sendd(f"/w {player} 你的手牌: {' '.join(hand)}"))
            else:
                ws.send(sendd("开始失败，需要至少2个玩家"))

        elif command == "status":
            # 查看游戏状态
            ws.send(sendd(uno_game.get_game_status()))

        elif command == "hand":
            # 查看手牌
            if nick in uno_game.players:
                hand = uno_game.get_player_hand(nick)
                ws.send(sendd(f"/w {nick} 你的手牌: {' '.join(hand)}"))
            else:
                ws.send(sendd("你不在游戏中"))

        elif command == "play":
            # 出牌
            if len(parts) < 3:
                ws.send(sendd("请指定要出的牌，格式: uno play 红5"))
                return

            card_str = parts[2]
            new_color = parts[3] if len(parts) > 3 else None

            success, message = uno_game.play_card(nick, card_str, new_color)
            ws.send(sendd(f"{nick}: {message}"))

            if success and uno_game.winner:
                # 玩家获胜，给予金币奖励
                trip = self.json_data.get("trip", "")
                coins_earned = 50  # UNO获胜奖励50金币
                new_total = user_system.add_coins(nick, coins_earned, trip)
                ws.send(sendd(f"🎉 恭喜 {nick} 获得 {coins_earned} 金币奖励！当前金币: {new_total}"))
            elif success and not uno_game.winner:
                # 显示游戏状态
                ws.send(sendd(uno_game.get_game_status()))
                # 私聊发送当前玩家手牌
                current_player = uno_game.get_current_player()
                if current_player:
                    hand = uno_game.get_player_hand(current_player)
                    ws.send(sendd(f"/w {current_player} 轮到你了！手牌: {' '.join(hand)}"))

        elif command == "draw":
            # 摸牌
            if nick == uno_game.get_current_player():
                drawn_cards = uno_game.draw_card(nick, 1)
                if drawn_cards:
                    ws.send(sendd(f"{nick} 摸了1张牌"))
                    uno_game.next_turn()
                    ws.send(sendd(uno_game.get_game_status()))
                    # 私聊发送新手牌
                    hand = uno_game.get_player_hand(nick)
                    ws.send(sendd(f"/w {nick} 你的手牌: {' '.join(hand)}"))
                else:
                    ws.send(sendd("没有牌可摸了"))
            else:
                ws.send(sendd("不是你的回合"))

        elif command == "uno":
            # 喊UNO
            success, message = uno_game.call_uno(nick)
            ws.send(sendd(f"{nick}: {message}"))

        elif command == "reset":
            # 重置游戏
            uno_game = UnoGame()
            ws.send(sendd("UNO游戏已重置"))

        else:
            ws.send(sendd("未知的UNO命令"))

    def handle_wordle_command(self):
        """处理 Wordle 游戏命令"""
        global wordle_game

        text = self.json_data["text"]
        nick = self.json_data["nick"]

        parts = text.split()
        if len(parts) < 2:
            ws.send(sendd("Wordle命令格式错误"))
            return

        command = parts[1]

        if command == "start":
            # 开始新游戏
            target = wordle_game.start_new_game()

            # 生成合并消息
            message_parts = ["🎯 Wordle 游戏开始！猜一个5字母英文单词"]

            # 生成并获取游戏图片
            image_url, local_path = self.get_wordle_image()
            if image_url:
                message_parts.append(f"![Wordle Game]({image_url})")
            elif local_path:
                message_parts.append(f"📷 图片已生成: {local_path} (上传失败，请检查网络)")

            message_parts.append(wordle_game.get_game_status())

            # 发送合并消息
            ws.send(sendd("\n \n".join(message_parts)))
            print(f"Wordle答案: {target}")  # 服务器日志

        elif command == "status":
            # 查看游戏状态
            message_parts = []

            # 生成并获取游戏图片
            if wordle_game.game_active or len(wordle_game.guesses) > 0:
                image_url, local_path = self.get_wordle_image()
                if image_url:
                    message_parts.append(f"![Wordle Game]({image_url})")
                elif local_path:
                    message_parts.append(f"📷 图片已生成: {local_path} (上传失败，请检查网络)")

            message_parts.append(wordle_game.get_game_status())

            # 发送合并消息
            ws.send(sendd("\n \n".join(message_parts)))

        else:
            ws.send(sendd("未知的Wordle命令，输入 'wordle' 查看帮助"))

    def handle_wordle_guess(self, word):
        """处理 Wordle 猜测"""
        global wordle_game

        nick = self.json_data["nick"]
        success, message = wordle_game.make_guess(word)

        if success:
            # 生成合并消息
            message_parts = [f"{nick} 猜测: {word.upper()}"]

            # 生成并获取游戏图片
            image_url, local_path = self.get_wordle_image()
            if image_url:
                message_parts.append(f"![Wordle Game]({image_url})")
            elif local_path:
                message_parts.append(f"📷 图片已生成: {local_path} (上传失败，请检查网络)")

            message_parts.append(wordle_game.get_game_status())

            if not wordle_game.game_active:
                # 游戏结束
                if len(wordle_game.guesses) > 0 and wordle_game.guesses[-1]["word"] == wordle_game.target_word:
                    # 玩家获胜，给予金币奖励和记录时间
                    trip = self.json_data.get("trip", "")
                    elapsed_time = int(time.time() - wordle_game.start_time) if wordle_game.start_time else 0

                    coins_earned = 30  # Wordle获胜奖励30金币
                    new_total = user_system.add_coins(nick, coins_earned, trip)

                    # 记录Wordle完成时间
                    is_record, best_time = user_system.record_wordle_time(nick, trip, elapsed_time)

                    reward_msg = f"🎉 恭喜 {nick} 获得 {coins_earned} 金币奖励！当前金币: {new_total}"
                    if is_record:
                        minutes = elapsed_time // 60
                        seconds = elapsed_time % 60
                        time_str = f"{minutes}分{seconds}秒" if minutes > 0 else f"{seconds}秒"
                        reward_msg += f"\n🏆 新纪录！完成时间: {time_str}"

                    message_parts.append(reward_msg)

                # 游戏结束，可以开始新游戏
                message_parts.append("输入 'wordle start' 开始新游戏")

            # 发送合并消息
            ws.send(sendd("\n \n".join(message_parts)))
        else:
            ws.send(sendd(f"{nick}: {message}"))

    def get_wordle_image(self):
        """获取 Wordle 游戏图片URL和本地路径"""
        try:
            # 生成图片并上传
            image_url, local_path = generate_wordle_image(wordle_game)
            return image_url, local_path
        except Exception as e:
            print(f"生成 Wordle 图片失败: {e}")
            return None, None

    def handle_main_help(self):
        """处理基础help命令"""
        nick = self.json_data["nick"]
        
        # 加载帮助信息
        try:
            with open('config/help.json', 'r', encoding='utf-8') as f:
                help_data = json.load(f)
        except FileNotFoundError:
            ws.send(sendd("/w " + nick + " 帮助文件未找到"))
            return
        except json.JSONDecodeError:
            ws.send(sendd("/w " + nick + " 帮助文件格式错误"))
            return

        # 显示主帮助
        help_info = help_data["main"]
        help_text = help_info["title"] + "\n\n"

        for section_title, section_content in help_info["sections"].items():
            help_text += section_title + ":\n"
            for item in section_content:
                help_text += "  " + item + "\n"
            help_text += "\n"

        # 发送帮助信息
        ws.send(sendd("/w " + nick + " " + help_text.strip()))

    def handle_simple_help_command(self):
        """处理简化的帮助命令 - 只支持直接指令名"""
        text = self.json_data["text"].strip()
        nick = self.json_data["nick"]
        
        # 特殊处理
        if text == "all":
            self.handle_help_all_command(nick)
            return

        # 加载帮助信息
        try:
            with open('config/help.json', 'r', encoding='utf-8') as f:
                help_data = json.load(f)
        except FileNotFoundError:
            ws.send(sendd("/w " + nick + " 帮助文件未找到"))
            return
        except json.JSONDecodeError:
            ws.send(sendd("/w " + nick + " 帮助文件格式错误"))
            return

        # 直接使用指令名作为帮助主题
        help_topic = text.lower()
        
        # 如果没有找到对应的帮助主题，显示主帮助
        if help_topic not in help_data:
            help_topic = "main"

        # 格式化帮助信息
        help_info = help_data[help_topic]
        help_text = help_info["title"] + "\n\n"

        for section_title, section_content in help_info["sections"].items():
            help_text += section_title + ":\n"
            for item in section_content:
                help_text += "  " + item + "\n"
            help_text += "\n"

        # 发送帮助信息
        ws.send(sendd("/w " + nick + " " + help_text.strip()))

    def handle_help_all_command(self, nick):
        """处理help all命令，输出command.txt内容"""
        try:
            with open('command.txt', 'r', encoding='utf-8') as f:
                command_content = f.read()
            
            # 按 ## 标题分割内容
            sections = []
            current_section = []
            
            for line in command_content.split('\n'):
                if line.startswith('## ') and current_section:
                    # 遇到新标题，保存当前段落
                    sections.append('\n'.join(current_section))
                    current_section = [line]
                else:
                    current_section.append(line)
            
            # 添加最后一个段落
            if current_section:
                sections.append('\n'.join(current_section))
            
            # 合并较短的段落，确保每段内容适中
            chunks = []
            current_chunk = ""
            max_length = 1500
            
            for section in sections:
                if len(current_chunk + "\n\n" + section) <= max_length:
                    if current_chunk:
                        current_chunk += "\n\n" + section
                    else:
                        current_chunk = section
                else:
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = section
            
            # 添加最后一个chunk
            if current_chunk:
                chunks.append(current_chunk)
            
            # 发送分块内容
            for i, chunk in enumerate(chunks):
                if len(chunks) > 1:
                    ws.send(sendd(f"/w {nick} 完整指令列表 ({i+1}/{len(chunks)}):\n{chunk}"))
                else:
                    ws.send(sendd(f"/w {nick} 完整指令列表:\n{chunk}"))
                
        except FileNotFoundError:
            ws.send(sendd(f"/w {nick} 指令文件未找到"))
        except Exception as e:
            ws.send(sendd(f"/w {nick} 读取指令文件失败: {str(e)}"))

    def handle_checkin_command(self):
        """处理签到命令"""
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        # 执行签到
        success, message, reward_details = user_system.daily_checkin(nick, trip)

        if success:
            # 构建签到成功消息
            total_reward = reward_details["total_reward"]
            base_reward = reward_details["base_reward"]
            rank_bonus = reward_details["rank_bonus"]
            consecutive_bonus = reward_details["consecutive_bonus"]
            rank = reward_details["rank"]
            consecutive_days = reward_details["consecutive_days"]
            new_total = reward_details["new_total"]
            used_double_card = reward_details["used_double_card"]

            success_msg = f"✅ {nick} 签到成功！"
            if rank <= 3:
                rank_emoji = ["🥇", "🥈", "🥉"][rank - 1]
                success_msg += f" {rank_emoji} 第{rank}名"
            else:
                success_msg += f" 第{rank}名"

            success_msg += f"\n💰 获得金币: {total_reward}"
            if used_double_card:
                success_msg += " (翻倍卡生效)"

            success_msg += f"\n   基础奖励: {base_reward}"
            if rank_bonus > 0:
                success_msg += f" + 排名奖励: {rank_bonus}"
            if consecutive_bonus > 0:
                success_msg += f" + 连续奖励: {consecutive_bonus}"

            success_msg += f"\n🔥 连续签到: {consecutive_days}天"
            success_msg += f"\n💎 当前金币: {new_total}"

            ws.send(sendd(success_msg))
        else:
            ws.send(sendd(f"{nick}: {message}"))

    def handle_inventory_command(self):
        """处理背包命令"""
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        items = user_system.get_user_inventory(nick, trip)
        coins = user_system.get_user_coins(nick, trip)

        if items:
            inventory_text = f"🎒 {nick} 的背包:\n💰 金币: {coins}\n\n📦 道具:\n"
            for item in items:
                inventory_text += f"• {item}\n"
            inventory_text += "\n💡 使用方法:\n• 成语接龙中输入 'skip' 使用跳过卡\n• Wordle游戏中输入 'hint' 使用提示卡"
        else:
            inventory_text = f"🎒 {nick} 的背包:\n💰 金币: {coins}\n\n📦 道具: 暂无\n\n🛒 可在商店购买道具"

        ws.send(sendd(f"/w {nick} {inventory_text}"))

    def handle_skip_card_command(self):
        """处理跳过卡命令"""
        global idiom_game

        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        # 检查是否有跳过卡
        user_key = user_system.init_user_data(nick, trip)
        skip_cards = user_system.user_data["users"][user_key]["inventory"]["idiom_skip_card"]

        if skip_cards <= 0:
            ws.send(sendd(f"{nick}: 你没有成语接龙跳过卡"))
            return

        # 使用跳过卡
        success, message = idiom_game.use_skip_card(nick)
        if success:
            # 消耗道具
            user_system.use_item(nick, trip, "idiom_skip_card")
            ws.send(sendd(f"🎫 {nick} 使用跳过卡！\n{message}"))
        else:
            ws.send(sendd(f"{nick}: {message}"))

    def handle_hint_card_command(self):
        """处理提示卡命令"""
        global wordle_game

        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")

        # 检查是否有提示卡
        user_key = user_system.init_user_data(nick, trip)
        hint_cards = user_system.user_data["users"][user_key]["inventory"]["wordle_hint_card"]

        if hint_cards <= 0:
            ws.send(sendd(f"{nick}: 你没有Wordle提示卡"))
            return

        # 使用提示卡
        success, message = wordle_game.use_hint_card()
        if success:
            # 消耗道具
            user_system.use_item(nick, trip, "wordle_hint_card")
            ws.send(sendd(f"🎫 {nick} 使用提示卡！\n{message}"))
        else:
            ws.send(sendd(f"{nick}: {message}"))

    def handle_welcome_command(self):
        """处理欢迎语命令"""
        nick = self.json_data["nick"]
        trip = self.json_data.get("trip", "")
        text = self.json_data["text"]

        parts = text.split()
        if len(parts) == 1:
            # 只输入wel，显示当前欢迎语状态
            user_key = user_system.init_user_data(nick, trip)
            user_data = user_system.user_data["users"][user_key]

            if user_data.get("custom_welcome"):
                status = "开启" if user_data.get("custom_welcome_enabled", False) else "关闭"
                ws.send(sendd(f"{nick} 的欢迎语: {user_data['custom_welcome']} (状态: {status})"))
            else:
                ws.send(sendd(f"{nick}: 你还没有设置自定义欢迎语"))
            return

        command = parts[1]

        if command == "on":
            # 开启欢迎语
            success, message = user_system.toggle_custom_welcome(nick, True, trip)
            ws.send(sendd(f"{nick}: {message}"))

        elif command == "off":
            # 关闭欢迎语
            success, message = user_system.toggle_custom_welcome(nick, False, trip)
            ws.send(sendd(f"{nick}: {message}"))

        else:
            # 更新欢迎语内容
            new_welcome = " ".join(parts[1:])

            # 检查用户是否有足够金币
            current_coins = user_system.get_user_coins(nick, trip)
            if current_coins < 1:
                ws.send(sendd(f"{nick}: 金币不足，更新欢迎语需要1金币"))
                return

            # 扣除金币并更新欢迎语
            user_system.add_coins(nick, -1, trip)
            success, message = user_system.update_custom_welcome(nick, new_welcome, trip)

            if success:
                ws.send(sendd(f"✅ {nick}: 欢迎语已更新！花费1金币\n新欢迎语: {new_welcome}"))
            else:
                # 如果更新失败，退还金币
                user_system.add_coins(nick, 1, trip)
                ws.send(sendd(f"❌ {nick}: {message}"))

    def handle_idiom_guess(self, idiom):
        """处理成语接龙猜测"""
        global idiom_game

        nick = self.json_data["nick"]

        # 只有在游戏进行中才处理成语猜测
        if not idiom_game.game_started:
            return

        # 检查是否是4字成语
        if len(idiom) != 4:
            return

        # 检查是否包含中文字符
        if not all('\u4e00' <= char <= '\u9fff' for char in idiom):
            return

        success, message = idiom_game.make_move(nick, idiom)
        if success:
            ws.send(sendd(message))



class main:
    def __init__(self,room,name):
        self.runbox = runbox(room,name)
        self.room = self.runbox.room
        self.name = self.runbox.name

    def on_message(self, ws, message):
        try:
            js_ms = json.loads(message)
            self.runbox.handle(js_ms)
        except json.JSONDecodeError as e:
            log_message("error", f"JSON解析失败: {e}")
        except Exception as e:
            log_message("error", f"消息处理失败: {e}")

    def on_error(self, ws, error):
        log_message("error", f"WebSocket错误: {error}")
        # 错误时不立即重连，等待 on_close 处理

    def on_close(self, ws, close_status_code=None, close_msg=None):
        if close_status_code:
            log_message("info", f"WebSocket连接关闭 - 状态码: {close_status_code}, 消息: {close_msg}")
        else:
            log_message("info", "WebSocket连接已关闭")

        # 连接关闭后，主循环会自动处理重连

    def on_open(self, ws):
        global first_connection
        log_message("info", "WebSocket 连接已建立")

        # 初始化机器人
        try:
            ws.send(json.dumps({"cmd": "join", "channel": str(self.room), "nick": str(self.name)}))
            time.sleep(1)
            ws.send(sendd("/color #F5DEB3"))

            # 只有第一次连接时发送欢迎消息
            if first_connection:
                time.sleep(1)
                ws.send(sendd("Hi，我是BoB"))
                first_connection = False
                log_message("info", f"机器人已加入房间，昵称: {self.name}")
            else:
                log_message("info", f"机器人已重连，昵称: {self.name}")

        except Exception as e:
            log_message("error", f"机器人初始化失败: {e}")

def stop_reconnect():
    """停止重连（用于优雅退出）"""
    global reconnect_running
    reconnect_running = False
    log_message("info", "重连已停止")

if __name__ == "__main__":
    try:
        # 初始化目录和文件
        log_message("info", "HackChat BoB 机器人启动中...")
        initialize_directories_and_files()

        # 初始化用户系统
        log_message("info", "初始化用户系统...")
        initialize_user_system()

        log_message("info", f"重连配置: 启用={ENABLE_RECONNECT}")

        # 启动WebSocket连接（包含重连机制）
        start_websocket_connection()

    except KeyboardInterrupt:
        log_message("info", "收到中断信号，正在退出...")
        stop_reconnect()
    except Exception as e:
        log_message("error", f"程序异常退出: {e}")
        stop_reconnect()
    finally:
        log_message("info", "机器人已退出")