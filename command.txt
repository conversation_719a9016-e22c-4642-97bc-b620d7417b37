# AIBoB 聊天机器人 - 完整指令列表
# 更新时间: 2025-08-04
# 说明: 所有可以在机器人中运行的指令和详细使用方法

## ===== AI对话系统 =====
@BoB <消息>                        # AI智能对话，支持上下文理解
@BoB_xxxx <消息>                   # 也可以使用完整昵称调用AI
delete                             # 删除个人AI聊天记录

## ===== 基础功能 =====
ping / 签到                        # 每日签到获得金币
coins                              # 查看当前金币数量
bag / 背包                         # 查看背包中的道具
history [数量]                     # 查看聊天历史记录(最多500条)
msg <用户> <内容>                  # 给其他用户留言

## ===== 欢迎语系统 =====
wel <内容>                         # 更新自定义欢迎语(需1金币)
wel on                             # 开启自定义欢迎语
wel off                            # 关闭自定义欢迎语

## ===== 排名系统 =====
rank list                          # 查看排行榜类型说明
rank 1                             # 查看金币排行榜(前10名)
rank 2                             # 查看Wordle计时排行榜
rank 3                             # 查看成语接龙轮次排行榜
rank <用户名>                      # 查看指定用户的金币排名

## ===== 商店系统 =====
shop list                          # 查看商店中的所有商品
buy 1 <欢迎语内容>                 # 购买自定义欢迎语(50金币)
buy 2                              # 购买签到金币翻倍卡(120金币)
buy 3                              # 购买成语接龙跳过卡(40金币)
buy 4                              # 购买Wordle提示卡(30金币)
buy 5                              # 购买神秘礼盒(80金币)
shop history                       # 查看购买历史记录

## ===== UNO游戏 =====
uno join                           # 加入UNO游戏
uno leave                          # 离开UNO游戏
uno start                          # 开始游戏(需要2-8人)
uno play <牌名>                    # 出牌(如: uno play 红5)
uno play <万能牌> <颜色>           # 出万能牌并指定颜色
uno draw                           # 摸牌
uno uno                            # 喊UNO(手牌剩1张时必须喊)
uno hand                           # 查看自己的手牌
uno status                         # 查看游戏状态
uno reset                          # 重置游戏

## ===== Wordle游戏 =====
wordle start                       # 开始新的Wordle游戏
<5字母英文单词>                    # 直接输入猜测(如: APPLE)
hint                               # 使用提示卡获得字母位置提示
wordle status                      # 查看当前游戏状态

## ===== 成语接龙游戏 =====
idiom join                         # 加入成语接龙游戏
idiom leave                        # 离开成语接龙游戏
idiom start                        # 开始游戏(需要2-8人)
<4字中文成语>                      # 直接输入成语进行接龙
skip                               # 使用跳过卡跳过本轮
idiom status                       # 查看游戏状态
idiom reset                        # 重置游戏

## ===== 帮助系统 =====
help                               # 查看基础帮助信息
uno                                # UNO游戏详细帮助
wordle                             # Wordle游戏详细帮助
idiom                              # 成语接龙游戏详细帮助

shop                               # 商店系统详细帮助
rank                               # 排名系统详细帮助
stock                              # 股票交易系统详细帮助
checkin                            # 签到系统帮助
inventory                          # 背包系统帮助
ai                                 # AI对话系统帮助
all                                # 显示此完整指令列表

## ===== 股票交易系统 =====
stock                              # 查看股票交易帮助和股票列表
stock list                         # 查看所有可交易股票(同stock)
stock get <代码> <数量>            # 买入股票(如: stock get 1 10)
stock sell <代码> <数量>           # 卖出股票(如: stock sell 1 5)
stock portfolio                    # 查看投资组合和总资产
stock price <代码>                 # 查看股票详情(如: stock price 1)

## ===== 简化股票指令 =====
get <代码> <数量>                  # 简化买入指令(如: get 1 10)
sell <代码> <数量>                 # 简化卖出指令(如: sell 1 5)
portfolio                          # 查看投资组合
price <代码>                       # 查看股票详情(如: price 1)

## ===== 金币获得方式 =====
1. 每日签到: 基础10金币 + 排名奖励(前3名额外奖励) + 连续签到奖励
2. UNO游戏获胜: 50金币
3. Wordle游戏获胜: 30金币(同时记录完成时间)
4. 成语接龙游戏: 第1名100金币，第2名60金币，第3名30金币

## ===== 重要说明 =====
- 金币和购买记录与昵称和识别码绑定，请保持一致
- 成语接龙现在支持拼音相同即可，不需要字完全相同
- 股票交易使用虚拟金币，价格为模拟数据但基于真实公司
- 支持的股票包括中美知名公司：苹果、微软、阿里巴巴、腾讯等
- 机器人昵称格式为"BoB_词语"，如BoB_Code、BoB_Star等
- AI调用支持@BoB和@BoB_完整昵称两种方式
- 所有帮助信息通过私聊发送，避免刷屏