# AIBoB - HackChat 聊天机器人依赖包
# 
# 核心功能依赖
websocket-client>=1.6.0    # WebSocket 客户端，用于连接 HackChat
requests>=2.31.0           # HTTP 请求库，用于 API 调用和文件上传
openai>=1.0.0              # OpenAI API 客户端，用于 AI 对话功能

# 图像处理依赖
Pillow>=10.0.0             # Python 图像处理库，用于 Wordle 游戏图片生成

# 可选依赖（用于文件锁定，Unix/Linux 系统）
# fcntl - 内置模块，Windows 系统不支持

# 标准库模块（无需安装）
# json - JSON 数据处理
# time - 时间处理
# random - 随机数生成
# subprocess - 子进程管理
# threading - 多线程支持
# os - 操作系统接口
# string - 字符串操作
# tempfile - 临时文件处理
# io - 输入输出操作
# html - HTML 转义处理
# sys - 系统相关参数和函数
# shutil - 高级文件操作
# re - 正则表达式（如果使用）

# 开发和测试依赖（可选）
# pytest>=7.0.0           # 单元测试框架
# pytest-asyncio>=0.21.0  # 异步测试支持
