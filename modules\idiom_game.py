#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成语接龙游戏模块
"""

import json
import random
import time
import threading
from typing import Dict, List, Optional, Tuple

class IdiomGame:
    """成语接龙游戏类"""
    
    def __init__(self, idiom_file_path="data/games/idiom.json"):
        self.idiom_file_path = idiom_file_path
        self.idioms = {}  # 成语字典 {成语: {first: 首字, last: 尾字, first_pinyin: 首字拼音, last_pinyin: 尾字拼音, ...}}
        self.first_pinyin_index = {}  # 首字拼音索引 {首字拼音: [成语列表]}
        self.last_pinyin_index = {}   # 尾字拼音索引 {尾字拼音: [成语列表]}
        
        # 游戏状态
        self.game_started = False
        self.players = []  # 玩家列表
        self.current_player_index = 0
        self.used_idioms = set()  # 已使用的成语
        self.current_idiom = None  # 当前成语
        self.current_last_pinyin = None  # 当前需要接的拼音
        self.eliminated_players = set()  # 已淘汰的玩家
        
        # 计时器相关
        self.turn_timer = None
        self.turn_start_time = None
        self.time_limit = 30  # 30秒时间限制
        
        # 回调函数
        self.on_player_timeout = None
        self.on_game_end = None
        
        # 延迟加载成语数据
        self._idioms_loaded = False
    
    def _load_idioms(self):
        """延迟加载成语数据"""
        if self._idioms_loaded:
            return
        
        try:
            print("正在加载成语数据...")
            with open(self.idiom_file_path, 'r', encoding='utf-8') as f:
                idiom_data = json.load(f)
            
            for item in idiom_data:
                word = item['word']
                first_char = item['first']
                last_char = item['last']
                first_pinyin = item.get('first', '')  # 首字拼音
                last_pinyin = item.get('last', '')    # 尾字拼音
                
                self.idioms[word] = {
                    'first': first_char,
                    'last': last_char,
                    'first_pinyin': first_pinyin,
                    'last_pinyin': last_pinyin,
                    'pinyin': item.get('pinyin', ''),
                    'explanation': item.get('explanation', '')
                }
                
                # 建立拼音索引
                if first_pinyin not in self.first_pinyin_index:
                    self.first_pinyin_index[first_pinyin] = []
                self.first_pinyin_index[first_pinyin].append(word)
                
                if last_pinyin not in self.last_pinyin_index:
                    self.last_pinyin_index[last_pinyin] = []
                self.last_pinyin_index[last_pinyin].append(word)
            
            self._idioms_loaded = True
            print(f"成语数据加载完成，共 {len(self.idioms)} 个成语")
            
        except Exception as e:
            print(f"加载成语数据失败: {e}")
            self.idioms = {}
    
    def add_player(self, player_name):
        """添加玩家"""
        if self.game_started:
            return False, "游戏已开始，无法加入"
        
        if player_name in self.players:
            return False, "你已经在游戏中了"
        
        if len(self.players) >= 8:
            return False, "游戏人数已满（最多8人）"
        
        self.players.append(player_name)
        return True, f"{player_name} 加入成语接龙游戏！当前玩家: {len(self.players)}人"
    
    def remove_player(self, player_name):
        """移除玩家"""
        if player_name not in self.players:
            return False, "你不在游戏中"
        
        if self.game_started:
            # 游戏中途退出，视为淘汰
            self.eliminated_players.add(player_name)
            if player_name == self.get_current_player():
                self._next_turn()
            return True, f"{player_name} 退出游戏"
        else:
            self.players.remove(player_name)
            return True, f"{player_name} 离开游戏"
    
    def start_game(self):
        """开始游戏"""
        if len(self.players) < 2:
            return False, "至少需要2个玩家才能开始游戏"
        
        if self.game_started:
            return False, "游戏已经开始了"
        
        # 加载成语数据
        self._load_idioms()
        if not self.idioms:
            return False, "成语数据加载失败，无法开始游戏"
        
        self.game_started = True
        self.current_player_index = 0
        self.used_idioms.clear()
        self.eliminated_players.clear()
        
        # 随机选择第一个成语
        first_idiom = random.choice(list(self.idioms.keys()))
        self.current_idiom = first_idiom
        self.current_last_pinyin = self.idioms[first_idiom]['last_pinyin']
        self.used_idioms.add(first_idiom)
        
        # 开始第一轮计时
        self._start_turn_timer()
        
        return True, f"成语接龙开始！第一个成语是：{first_idiom}，请 {self.get_current_player()} 接龙（需要以'{self.current_last_pinyin}'音开头的成语）"
    
    def get_current_player(self):
        """获取当前玩家"""
        active_players = [p for p in self.players if p not in self.eliminated_players]
        if not active_players:
            return None
        return active_players[self.current_player_index % len(active_players)]
    
    def make_move(self, player_name, idiom):
        """玩家出招"""
        if not self.game_started:
            return False, "游戏未开始"

        if player_name in self.eliminated_players:
            return False, "你已被淘汰"

        current_player = self.get_current_player()
        if player_name != current_player:
            return False, f"现在是 {current_player} 的回合"

        # 验证成语
        valid, message = self._validate_idiom(idiom)
        if not valid:
            # 答错了，淘汰玩家
            self.eliminated_players.add(player_name)
            self._cancel_turn_timer()

            # 检查游戏是否结束
            if self._check_game_end():
                return True, f"{player_name} 答错被淘汰！{message}\n{self._get_game_end_message()}"

            # 继续下一轮
            self._next_turn()
            next_player = self.get_current_player()
            self._start_turn_timer()
            return True, f"{player_name} 答错被淘汰！{message}\n现在轮到 {next_player}，请接'{self.current_last_pinyin}'音"

        # 答对了
        self.current_idiom = idiom
        self.current_last_pinyin = self.idioms[idiom]['last_pinyin']
        self.used_idioms.add(idiom)

        self._cancel_turn_timer()

        # 检查游戏是否结束
        if self._check_game_end():
            return True, f"{player_name} 接龙成功：{idiom}\n{self._get_game_end_message()}"

        # 继续下一轮
        self._next_turn()
        next_player = self.get_current_player()
        self._start_turn_timer()

        return True, f"{player_name} 接龙成功：{idiom}\n现在轮到 {next_player}，请接'{self.current_last_pinyin}'音"

    def use_skip_card(self, player_name):
        """使用跳过卡"""
        if not self.game_started:
            return False, "游戏未开始"

        if player_name in self.eliminated_players:
            return False, "你已被淘汰"

        current_player = self.get_current_player()
        if player_name != current_player:
            return False, f"现在是 {current_player} 的回合"

        # 取消当前计时器
        self._cancel_turn_timer()

        # 检查游戏是否结束
        if self._check_game_end():
            return True, f"{player_name} 使用跳过卡跳过本轮\n{self._get_game_end_message()}"

        # 继续下一轮
        self._next_turn()
        next_player = self.get_current_player()
        self._start_turn_timer()

        return True, f"{player_name} 使用跳过卡跳过本轮\n现在轮到 {next_player}，请接'{self.current_last_pinyin}'音"

    def _validate_idiom(self, idiom):
        """验证成语是否有效"""
        # 检查是否是有效成语
        if idiom not in self.idioms:
            return False, f"'{idiom}' 不是有效的成语"

        # 检查是否已经使用过
        if idiom in self.used_idioms:
            return False, f"'{idiom}' 已经使用过了"

        # 检查首字拼音是否匹配
        if self.idioms[idiom]['first_pinyin'] != self.current_last_pinyin:
            return False, f"'{idiom}' 的首字拼音不是'{self.current_last_pinyin}'"

        return True, "成语有效"

    def _next_turn(self):
        """下一轮"""
        active_players = [p for p in self.players if p not in self.eliminated_players]
        if len(active_players) > 1:
            self.current_player_index = (self.current_player_index + 1) % len(active_players)

    def _start_turn_timer(self):
        """开始回合计时"""
        self._cancel_turn_timer()
        self.turn_start_time = time.time()
        self.turn_timer = threading.Timer(self.time_limit, self._on_timeout)
        self.turn_timer.start()

    def _cancel_turn_timer(self):
        """取消回合计时"""
        if self.turn_timer:
            self.turn_timer.cancel()
            self.turn_timer = None

    def _on_timeout(self):
        """超时处理"""
        current_player = self.get_current_player()
        if current_player:
            self.eliminated_players.add(current_player)

            if self.on_player_timeout:
                self.on_player_timeout(current_player)

            # 注意：游戏结束检查由on_player_timeout回调处理，这里不再重复检查

    def _check_game_end(self):
        """检查游戏是否结束"""
        active_players = [p for p in self.players if p not in self.eliminated_players]
        return len(active_players) <= 1

    def _get_game_end_message(self):
        """获取游戏结束消息"""
        active_players = [p for p in self.players if p not in self.eliminated_players]
        if len(active_players) == 1:
            winner = active_players[0]
            return f"🎉 游戏结束！{winner} 获胜！"
        else:
            return "游戏结束！"

    def get_game_status(self):
        """获取游戏状态"""
        if not self.game_started:
            return f"成语接龙游戏 - 等待开始\n玩家: {', '.join(self.players)} ({len(self.players)}人)"

        active_players = [p for p in self.players if p not in self.eliminated_players]
        current_player = self.get_current_player()

        status = f"成语接龙游戏 - 进行中\n"
        status += f"当前成语: {self.current_idiom}\n"
        status += f"需要接: '{self.current_last_pinyin}' 音开头的成语\n"
        status += f"当前玩家: {current_player}\n"

        if self.turn_start_time:
            elapsed = int(time.time() - self.turn_start_time)
            remaining = max(0, self.time_limit - elapsed)
            status += f"剩余时间: {remaining}秒\n"

        status += f"剩余玩家: {', '.join(active_players)} ({len(active_players)}人)"

        if self.eliminated_players:
            status += f"\n已淘汰: {', '.join(self.eliminated_players)}"

        return status

    def get_rankings(self):
        """获取排名（按淘汰顺序）"""
        active_players = [p for p in self.players if p not in self.eliminated_players]
        eliminated_list = list(self.eliminated_players)

        # 返回排名：[获胜者, 第二名, 第三名, ...]
        rankings = []
        if len(active_players) == 1:
            rankings.append(active_players[0])  # 获胜者

        # 按淘汰顺序倒序（后淘汰的排名更高）
        eliminated_list.reverse()
        rankings.extend(eliminated_list)

        return rankings

    def reset_game(self):
        """重置游戏"""
        self._cancel_turn_timer()
        self.game_started = False
        self.players.clear()
        self.current_player_index = 0
        self.used_idioms.clear()
        self.current_idiom = None
        self.current_last_pinyin = None
        self.eliminated_players.clear()
        self.turn_start_time = None
