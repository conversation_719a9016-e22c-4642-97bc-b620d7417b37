#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML 历史记录生成器
"""

import html
import time
from .file_upload import file_uploader

class HistoryHTMLGenerator:
    """历史记录 HTML 生成器"""
    
    def __init__(self):
        self.default_limit = 30  # 默认显示条数，刚好不超过文本显示限制
        self.max_text_length = 800  # 文本显示的最大长度
    
    def generate_html(self, messages, room_name, date_str):
        """生成历史记录 HTML"""
        
        # HTML 模板
        html_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIBoB History Engine - {room_name} ({date_str})</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border: 3px solid #000000;
            padding: 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        
        .header {{
            background-color: #000000;
            color: white;
            padding: 15px 20px;
            margin: 0;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }}
        
        .header .info {{
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }}
        
        .content {{
            padding: 20px;
        }}
        
        .message {{
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .message:last-child {{
            border-bottom: none;
        }}
        
        .message-time {{
            color: #666;
            font-size: 12px;
            font-weight: normal;
            margin-right: 8px;
        }}
        
        .message-nick {{
            font-weight: bold;
            color: #2c3e50;
            margin-right: 8px;
        }}
        
        .message-text {{
            color: #333;
            word-wrap: break-word;
            white-space: pre-wrap;
        }}
        
        .stats {{
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
            text-align: center;
        }}
        
        @media (max-width: 600px) {{
            body {{
                padding: 10px;
            }}
            
            .container {{
                border-width: 2px;
            }}
            
            .header {{
                padding: 12px 15px;
            }}
            
            .header h1 {{
                font-size: 20px;
            }}
            
            .content {{
                padding: 15px;
            }}
            
            .message {{
                margin-bottom: 10px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AIBoB History Engine</h1>
            <div class="info">房间: {room_name} | 日期: {date_str} | 共 {message_count} 条消息</div>
        </div>
        
        <div class="content">
            {messages_html}
        </div>
        
        <div class="stats">
            生成时间: {generate_time} | AIBoB From Su
        </div>
    </div>
</body>
</html>"""
        
        # 生成消息 HTML
        messages_html = ""
        for msg in messages:
            # 安全转义 HTML 字符
            time_str = html.escape(msg.get("time", ""))
            nick = html.escape(msg.get("nick", "unknown"))
            text = html.escape(msg.get("text", ""))
            
            message_html = f"""            <div class="message">
                <span class="message-time">{time_str}</span>
                <span class="message-nick">{nick}:</span>
                <span class="message-text">{text}</span>
            </div>"""
            
            messages_html += message_html + "\n"
        
        # 填充模板
        html_content = html_template.format(
            room_name=html.escape(room_name),
            date_str=html.escape(date_str),
            message_count=len(messages),
            messages_html=messages_html,
            generate_time=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        return html_content
    
    def upload_html_file(self, html_content, room_name, date_str):
        """上传 HTML 文件"""
        try:
            # 生成文件名
            safe_room = room_name.replace(" ", "_").replace("/", "_")
            filename = f"history_{safe_room}_{date_str}.html"

            # 上传文件，指定 HTML MIME 类型
            success, result = file_uploader.upload_text_file(html_content, filename, 'text/html')
            return success, result

        except Exception as e:
            return False, f"上传失败: {str(e)}"
    
    def format_messages_for_display(self, messages):
        """格式化消息用于文本显示"""
        formatted_lines = []
        for msg in messages:
            time_str = msg.get("time", "")
            nick = msg.get("nick", "unknown")
            text = msg.get("text", "")
            formatted_lines.append(f"{time_str} {nick}: {text}")
        
        return "\n".join(formatted_lines)
    
    def should_upload_as_file(self, messages):
        """判断是否应该上传为文件"""
        # 计算文本长度
        text_content = self.format_messages_for_display(messages)
        return len(text_content) > self.max_text_length
    
    def process_history_request(self, messages, room_name, date_str, requested_count=None):
        """处理历史记录请求"""
        # 确定显示数量
        if requested_count is None:
            display_count = min(self.default_limit, len(messages))
        else:
            display_count = min(requested_count, len(messages))
        
        # 获取要显示的消息
        display_messages = messages[-display_count:] if display_count > 0 else []
        
        # 判断是否需要上传文件
        if self.should_upload_as_file(display_messages):
            # 生成 HTML 并上传
            html_content = self.generate_html(display_messages, room_name, date_str)
            success, result = self.upload_html_file(html_content, room_name, date_str)
            
            if success:
                return "file", result, len(display_messages)
            else:
                # 上传失败，回退到文本
                text_content = self.format_messages_for_display(display_messages)
                return "text_fallback", text_content, len(display_messages)
        else:
            # 直接文本显示
            text_content = self.format_messages_for_display(display_messages)
            return "text", text_content, len(display_messages)

# 全局 HTML 生成器实例
html_generator = HistoryHTMLGenerator()

def process_history_request(messages, room_name, date_str, requested_count=None):
    """处理历史记录请求的便捷函数"""
    return html_generator.process_history_request(messages, room_name, date_str, requested_count)
