{"timestamp": 1754146438.6983411, "datetime": "2025-08-02 22:53:58", "room": "lounges", "nick": "BoB_OZLO", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754146444.3179126, "datetime": "2025-08-02 22:54:04", "room": "lounges", "nick": "BoB_OZLO", "trip": "BuR9sE", "text": "哇！su！今天也要开开心心的过啊！", "cmd": "chat"}
{"timestamp": 1754146449.8034074, "datetime": "2025-08-02 22:54:09", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "@BoB_OZLO 你好", "cmd": "chat"}
{"timestamp": 1754146450.0236347, "datetime": "2025-08-02 22:54:10", "room": "lounges", "nick": "Bo<PERSON>_OZLO", "trip": "BuR9sE", "text": "su，你好！有什么我可以帮助你的吗？", "cmd": "chat", "custom_id": "117761", "updated_at": "2025-08-02 22:54:18"}
{"timestamp": 1754146462.122037, "datetime": "2025-08-02 22:54:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754146468.7132697, "datetime": "2025-08-02 22:54:28", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754146468.9327211, "datetime": "2025-08-02 22:54:28", "room": "lounges", "nick": "BoB_OZLO", "trip": "BuR9sE", "text": "su 当前拥有 0 金币", "cmd": "chat"}
{"timestamp": 1754146473.7329803, "datetime": "2025-08-02 22:54:33", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "checkin", "cmd": "chat"}
{"timestamp": 1754146473.948911, "datetime": "2025-08-02 22:54:33", "room": "lounges", "nick": "BoB_OZLO", "trip": "BuR9sE", "text": "✅ su 签到成功！ 🥈 第2名\n💰 获得金币: 25\n   基础奖励: 10 + 排名奖励: 15\n🔥 连续签到: 1天\n💎 当前金币: 25", "cmd": "chat"}
{"timestamp": 1754146481.75246, "datetime": "2025-08-02 22:54:41", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754146481.9664779, "datetime": "2025-08-02 22:54:41", "room": "lounges", "nick": "BoB_OZLO", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 50 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754146546.3946998, "datetime": "2025-08-02 22:55:46", "room": "lounges", "nick": "BoB_XK7D", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754146551.5990613, "datetime": "2025-08-02 22:55:51", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754146552.4432344, "datetime": "2025-08-02 22:55:52", "room": "lounges", "nick": "BoB_XK7D", "trip": "BuR9sE", "text": "su 当前拥有 1556454 金币", "cmd": "chat"}
{"timestamp": 1754146628.8377519, "datetime": "2025-08-02 22:57:08", "room": "lounges", "nick": "BoB_UN0X", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754146687.730057, "datetime": "2025-08-02 22:58:07", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754146693.2563803, "datetime": "2025-08-02 22:58:13", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help shop", "cmd": "chat"}
{"timestamp": 1754146727.8487952, "datetime": "2025-08-02 22:58:47", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 1", "cmd": "chat"}
{"timestamp": 1754146728.0994546, "datetime": "2025-08-02 22:58:48", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 请提供欢迎语内容", "cmd": "chat"}
{"timestamp": 1754146735.4462392, "datetime": "2025-08-02 22:58:55", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 1 原神启动！", "cmd": "chat"}
{"timestamp": 1754146735.674245, "datetime": "2025-08-02 22:58:55", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 自定义欢迎语！\n流水单号: ORD1467356E9K\n花费: 100 金币", "cmd": "chat"}
{"timestamp": 1754146743.0643222, "datetime": "2025-08-02 22:59:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop history", "cmd": "chat"}
{"timestamp": 1754146762.2041893, "datetime": "2025-08-02 22:59:22", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "哇！su！今天天气真不错呢！", "cmd": "chat"}
{"timestamp": 1754146788.7461793, "datetime": "2025-08-02 22:59:48", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754146794.9326859, "datetime": "2025-08-02 22:59:54", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank", "cmd": "chat"}
{"timestamp": 1754146795.1498754, "datetime": "2025-08-02 22:59:55", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "🏆 金币排行榜 (前10名):\n🥇 1. su - 1556354 金币\n🥈 2. TestUser - 1013 金币", "cmd": "chat"}
{"timestamp": 1754146803.634884, "datetime": "2025-08-02 23:00:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help rank", "cmd": "chat"}
{"timestamp": 1754146820.4615269, "datetime": "2025-08-02 23:00:20", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754146820.6893716, "datetime": "2025-08-02 23:00:20", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 50 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754146845.058555, "datetime": "2025-08-02 23:00:45", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146845.2867184, "datetime": "2025-08-02 23:00:45", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146845RJ9O\n花费: 100 金币\n\n🎁 开启结果:\n🪙 恭喜获得 61 金币！", "cmd": "chat"}
{"timestamp": 1754146856.0970895, "datetime": "2025-08-02 23:00:56", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146856.3171623, "datetime": "2025-08-02 23:00:56", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146856J00H\n花费: 100 金币\n\n🎁 开启结果:\n💔 很遗憾，这次什么都没有...", "cmd": "chat"}
{"timestamp": 1754146872.0584383, "datetime": "2025-08-02 23:01:12", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 4", "cmd": "chat"}
{"timestamp": 1754146872.2857664, "datetime": "2025-08-02 23:01:12", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 Wordle提示卡！\n流水单号: ORD146872DNGY\n花费: 60 金币\n已添加到背包", "cmd": "chat"}
{"timestamp": 1754146879.5713747, "datetime": "2025-08-02 23:01:19", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help shop", "cmd": "chat"}
{"timestamp": 1754146928.4911675, "datetime": "2025-08-02 23:02:08", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146928.7254517, "datetime": "2025-08-02 23:02:08", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146928A6EZ\n花费: 100 金币\n\n🎁 开启结果:\n🪙 恭喜获得 139 金币！", "cmd": "chat"}
{"timestamp": 1754146931.9815705, "datetime": "2025-08-02 23:02:11", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146932.1990633, "datetime": "2025-08-02 23:02:12", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD1469313J1Z\n花费: 100 金币\n\n🎁 开启结果:\n💔 很遗憾，这次什么都没有...", "cmd": "chat"}
{"timestamp": 1754146935.0200007, "datetime": "2025-08-02 23:02:15", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146935.2439382, "datetime": "2025-08-02 23:02:15", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146935GU99\n花费: 100 金币\n\n🎁 开启结果:\n💔 很遗憾，这次什么都没有...", "cmd": "chat"}
{"timestamp": 1754146937.3092055, "datetime": "2025-08-02 23:02:17", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146937.529192, "datetime": "2025-08-02 23:02:17", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146937MYIY\n花费: 100 金币\n\n🎁 开启结果:\n🪙 恭喜获得 106 金币！", "cmd": "chat"}
{"timestamp": 1754146944.760141, "datetime": "2025-08-02 23:02:24", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146944.9825447, "datetime": "2025-08-02 23:02:24", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD146944K96D\n花费: 100 金币\n\n🎁 开启结果:\n💔 很遗憾，这次什么都没有...", "cmd": "chat"}
{"timestamp": 1754146947.5812511, "datetime": "2025-08-02 23:02:27", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop buy 5", "cmd": "chat"}
{"timestamp": 1754146947.809259, "datetime": "2025-08-02 23:02:27", "room": "lounges", "nick": "BoB_YZ19", "trip": "BuR9sE", "text": "su: 成功购买 神秘礼盒！\n流水单号: ORD1469475EBM\n花费: 100 金币\n\n🎁 开启结果:\n🎫 恭喜获得Wordle提示卡 x1！", "cmd": "chat"}
{"timestamp": 1754147122.6965585, "datetime": "2025-08-02 23:05:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "背包", "cmd": "chat"}
{"timestamp": 1754147147.9916508, "datetime": "2025-08-02 23:05:47", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wordle start", "cmd": "chat"}
{"timestamp": 1754147149.502233, "datetime": "2025-08-02 23:05:49", "room": "lounges", "nick": "BoB_5A1S", "trip": "BuR9sE", "text": "🎯 Wordle 游戏开始！猜一个5字母英文单词\n \n![Wordle Game](https://i.gyazo.com/2fa299c0a3d5afe5aa6a1a01812143a5.png)\n \nWordle 游戏 (0/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754147157.553108, "datetime": "2025-08-02 23:05:57", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "start", "cmd": "chat"}
{"timestamp": 1754147159.4477854, "datetime": "2025-08-02 23:05:59", "room": "lounges", "nick": "BoB_5A1S", "trip": "BuR9sE", "text": "su 猜测: START\n \n![Wordle Game](https://i.gyazo.com/43818ebc7695f5bcfb1ad3f08b048de4.png)\n \nWordle 游戏 (1/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754147163.9351475, "datetime": "2025-08-02 23:06:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "hint", "cmd": "chat"}
{"timestamp": 1754147164.619222, "datetime": "2025-08-02 23:06:04", "room": "lounges", "nick": "BoB_5A1S", "trip": "BuR9sE", "text": "🎫 su 使用提示卡！\n💡 提示：第 4 位是字母 'O'", "cmd": "chat"}
{"timestamp": 1754147185.21687, "datetime": "2025-08-02 23:06:25", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stood", "cmd": "chat"}
{"timestamp": 1754147186.67885, "datetime": "2025-08-02 23:06:26", "room": "lounges", "nick": "BoB_5A1S", "trip": "BuR9sE", "text": "su 猜测: STOOD\n \n![Wordle Game](https://i.gyazo.com/52b14311692b7a6e795a4da3b7dd3614.png)\n \nWordle 游戏 (2/6) - 🎉 获胜！用时 38 秒\n \n🎉 恭喜 su 获得 30 金币奖励！当前金币: 1555830\n🏆 新纪录！完成时间: 38秒\n \n输入 'wordle start' 开始新游戏", "cmd": "chat"}
{"timestamp": 1754147213.7177231, "datetime": "2025-08-02 23:06:53", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754147246.3557463, "datetime": "2025-08-02 23:07:26", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754147246.5779345, "datetime": "2025-08-02 23:07:26", "room": "lounges", "nick": "BoB_5A1S", "trip": "BuR9sE", "text": "su 当前拥有 1555830 金币", "cmd": "chat"}
