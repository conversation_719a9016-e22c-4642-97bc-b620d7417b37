# AIBoB 聊天机器人详细使用说明

## 🤖 机器人基本信息

**机器人名称**：BoB（随机昵称格式：BoB_XXXX#8888）  
**平台**：HackChat 聊天室  
**主要功能**：AI对话、游戏娱乐、金币系统、排名竞技

---

## 📋 目录

1. [AI 对话系统](#ai-对话系统)
2. [金币系统](#金币系统)
3. [商店系统](#商店系统)
4. [排名系统](#排名系统)
5. [游戏系统](#游戏系统)
6. [留言系统](#留言系统)
7. [历史记录](#历史记录)
8. [帮助系统](#帮助系统)

---

## 🧠 AI 对话系统

### 使用方法
```
@BoB 你的问题或对话内容
```

### 功能说明
- 使用 GPT-4o-mini 模型进行智能对话
- 支持中英文混合对话
- 维护个人聊天历史记录
- 支持上下文理解

### 返回内容
- **处理中提示**：`用户名，正在分析并生成回答，请稍后...`
- **AI回复**：`用户名，[AI生成的回答内容]`
- **错误情况**：`用户名，命令执行失败: [错误信息]`

### 使用示例
```
@BoB 你好，今天天气怎么样？
@BoB 帮我写一首关于春天的诗
@BoB 解释一下什么是人工智能
```

### 特殊功能
- **删除聊天记录**：输入 `delete` 可删除个人AI聊天历史
- **返回结果**：`聊天记录已删除` 或 `找不到聊天记录文件`

---

## 🪙 金币系统

### 查看金币
```
coins
```
**返回**：`用户名 当前拥有 X 金币`

### 获得金币的方式

#### 1. UNO游戏获胜
- **奖励**：50金币
- **触发条件**：成功出完所有手牌
- **返回**：`🎉 恭喜 用户名 获得 50 金币奖励！当前金币: X`

#### 2. Wordle游戏获胜
- **奖励**：30金币
- **触发条件**：在6次机会内猜出正确单词
- **返回**：`🎉 恭喜 用户名 获得 30 金币奖励！当前金币: X`
- **额外奖励**：如果创造新的时间记录，会显示 `🏆 新纪录！完成时间: X分X秒`

#### 3. 成语接龙游戏获胜
- **第1名**：100金币
- **第2名**：60金币  
- **第3名**：30金币
- **触发条件**：游戏结束时排名前三（需要3人以上参与）
- **返回**：`🏆 第X名 用户名: +X金币 (总计: X)`

### 金币特性
- 金币与用户昵称和识别码(trip)绑定
- 数据持久化保存
- 支持跨会话累积

---

## 🛒 商店系统

### 查看商店
```
shop
```
**返回**：
```
🛒 商店物品:
[1] 自定义欢迎语 - 100 金币
    设置专属的个人欢迎语
    更新价格: 50 金币

使用 'shop buy <商品号>' 购买物品
例如: shop buy 1 你的欢迎语
```

### 购买物品
```
shop buy 1 你的自定义欢迎语内容
```
**成功返回**：`用户名: 购买成功！流水号: XXXXXX，花费 100 金币，当前余额: X 金币`  
**失败返回**：`用户名: 金币不足，需要 100 金币，当前只有 X 金币`

### 更新已购买物品
```
shop update welcome 新的欢迎语内容
```
**成功返回**：`用户名: 欢迎语更新成功！花费 50 金币，当前余额: X 金币`  
**失败返回**：`用户名: 您还没有购买自定义欢迎语，请先购买`

### 开关功能
```
shop toggle welcome on   # 开启
shop toggle welcome off  # 关闭
```
**返回**：`用户名: 自定义欢迎语已开启/关闭`

### 查看购买历史
```
shop history
```
**返回**：
```
🛒 购买历史:
• 2024-01-01 12:00:00 - 自定义欢迎语
  流水号: ABC123 | 花费: 100金币
• 2024-01-02 15:30:00 - 自定义欢迎语更新
  流水号: DEF456 | 花费: 50金币
```

---

## 🏆 排名系统

### 金币排行榜
```
rank
```
**返回**：
```
🏆 金币排行榜 (前10名):
🥇 1. Alice - 500 金币
🥈 2. Bob - 350 金币
🥉 3. Charlie - 200 金币
   4. David - 150 金币
   ...
```

### Wordle计时排行榜
```
rank wordle
```
**返回**：
```
⏱️ Wordle计时排行榜 (前10名):
🥇 1. Alice - 45秒
🥈 2. Bob - 1分20秒
🥉 3. Charlie - 2分15秒
   ...
```

### 成语接龙排行榜
```
rank idiom
```
**返回**：
```
🀄 成语接龙排行榜 (前10名):
🥇 1. Alice - 25 轮
🥈 2. Bob - 18 轮
🥉 3. Charlie - 12 轮
   ...
```

### 查看个人排名
```
rank 用户名
```
**返回**：`用户名 的排名: 第 X 名，拥有 X 金币`  
**无记录**：`用户 用户名 暂无金币记录`

---

## 🎮 游戏系统

### 🎯 Wordle 游戏

#### 开始游戏
```
wordle start
```
**返回**：
```
🎯 Wordle 游戏开始！猜一个5字母英文单词
![Wordle Game](图片链接)
剩余猜测次数: 6/6
已猜测单词: 无
```

#### 进行猜测
```
APPLE  # 直接输入5个英文字母
```
**返回**：
```
用户名 猜测: APPLE
![Wordle Game](更新后的图片)
剩余猜测次数: 5/6
已猜测单词: APPLE
```

#### 查看游戏状态
```
wordle status
```
**返回**：当前游戏状态和图片

#### 游戏结果
- **获胜**：显示恭喜信息、金币奖励、可能的新纪录
- **失败**：显示正确答案和鼓励信息
- **游戏结束**：提示可以开始新游戏

### 🃏 UNO 游戏

#### 加入游戏
```
uno join
```
**返回**：`用户名 加入了UNO游戏！当前玩家: X人`

#### 离开游戏
```
uno leave
```
**返回**：`用户名 离开了UNO游戏`

#### 开始游戏
```
uno start
```
**返回**：
```
🎮 UNO游戏开始！
当前玩家: Alice, Bob, Charlie
轮到: Alice
顶牌: 红5
```
**私聊手牌**：`你的手牌: 红3 蓝7 黄跳过 绿2 万能 万能+4`

#### 出牌
```
uno play 红3
uno play 万能 蓝  # 万能牌需要指定颜色
```
**返回**：`用户名: 出牌成功！` 或错误信息

#### 摸牌
```
uno draw
```
**返回**：`用户名 摸了1张牌`

#### 喊UNO
```
uno uno
```
**返回**：`用户名: UNO！` 或相关提示

#### 查看手牌
```
uno hand
```
**私聊返回**：`你的手牌: [手牌列表]`

#### 查看游戏状态
```
uno status
```
**返回**：当前游戏状态信息

### 🀄 成语接龙游戏

#### 加入游戏
```
idiom join
```
**返回**：`用户名 加入了成语接龙游戏！当前玩家: X人`

#### 开始游戏
```
idiom start
```
**返回**：
```
🀄 成语接龙游戏开始！
参与玩家: Alice, Bob, Charlie
首个成语: 四面楚歌
现在轮到 Alice，请接'歌'
时间限制: 30秒
```

#### 进行接龙
```
歌舞升平  # 直接输入4个中文字符的成语
```
**成功返回**：`用户名 接龙: 歌舞升平 ✓ 现在轮到 Bob，请接'平'`  
**失败返回**：`用户名: 成语无效或不符合接龙规则`

#### 超时处理
**自动返回**：`⏰ 用户名 超时被淘汰！现在轮到 下一位玩家，请接'X'`

#### 游戏结束
**返回**：
```
🎉 成语接龙游戏结束！
🏆 第1名 Alice: +100金币 (总计: X) 🆕新纪录: 25轮!
🏆 第2名 Bob: +60金币 (总计: X)
🏆 第3名 Charlie: +30金币 (总计: X)
```

#### 查看游戏状态
```
idiom status
```
**返回**：当前游戏状态、参与玩家、当前轮次等信息

---

## 📨 留言系统

### 留言给其他用户
```
msg 目标用户名 留言内容
```
**返回**：`已给 目标用户名 留言成功！当 目标用户名 进入房间时会收到您的消息。`

### 接收留言
当用户进入房间时，如果有留言会自动私聊发送：
**私聊内容**：
```
您有新留言:
[2024-01-01 12:00:00] 发送者: 留言内容1
[2024-01-01 15:30:00] 发送者: 留言内容2
```

### 留言特性
- 留言会在用户下次进入房间时自动发送
- 发送后自动删除，避免重复通知
- 支持多条留言累积

---

## 📚 历史记录

### 查看聊天历史
```
history        # 查看默认数量的历史记录
history 20     # 查看最近20条记录（最多500条）
```

### 返回方式

#### HTML文件方式（优先）
**私聊返回**：`📄 历史记录已生成 HTML 文件: [文件链接] 包含最近 X 条消息`

#### 文本方式（备选）
**私聊返回**：
```
最近 X 条消息:
[12:00] Alice: 大家好
[12:01] Bob: 你好Alice
[12:02] Charlie: 今天天气不错
...
```

#### 分块发送（文件上传失败时）
**私聊返回**：
```
历史记录 (1/3):
[消息内容第一部分]

历史记录 (2/3):
[消息内容第二部分]
...
⚠️ HTML 文件上传失败，已分块显示
```

### 历史记录特性
- 只显示当天的聊天记录
- 过滤系统消息，只显示用户聊天
- 支持HTML格式美化显示
- 自动处理长内容分块

---

## ❓ 帮助系统

### 查看主帮助
```
help
```
**返回**：主要功能列表和使用提示

### 查看特定功能帮助
```
help ai       # AI对话帮助
help uno      # UNO游戏帮助  
help wordle   # Wordle游戏帮助
help idiom    # 成语接龙帮助
help coins    # 金币系统帮助
help shop     # 商店系统帮助
help rank     # 排名系统帮助
```
**返回**：对应功能的详细使用说明

### 帮助特性
- 所有帮助信息通过私聊发送
- 分类详细，便于查找
- 包含使用示例和注意事项

---

## 🎊 欢迎系统

### 默认欢迎
当用户进入房间时，机器人会发送随机欢迎语：
- `你好鸭~用户名！今天也要开开心心的过啊！`
- `欢迎欢迎~用户名！OvO！`
- `哇！用户名！今天也是幸运的一天！`

### 自定义欢迎（需购买）
购买自定义欢迎语后，会收到个性化的私聊欢迎消息。

### 新用户特殊欢迎
首次进入的用户会收到额外的私聊介绍信息。

---

## ⚠️ 注意事项

1. **身份验证**：金币和购买记录与昵称+识别码绑定
2. **游戏限制**：某些游戏需要最少参与人数
3. **命令格式**：严格按照示例格式输入命令
4. **网络依赖**：AI对话和图片生成需要网络连接
5. **数据持久化**：所有用户数据会自动保存
6. **重连机制**：机器人支持断线自动重连

---

## 🔧 故障排除

### 常见问题
- **AI不回复**：检查网络连接或稍后重试
- **游戏无响应**：确认游戏状态和命令格式
- **金币异常**：联系管理员检查数据文件
- **图片不显示**：可能是上传服务暂时不可用

### 紧急命令
- `delete`：删除个人AI聊天记录
- 各游戏的 `reset` 命令：重置游戏状态

---

*最后更新：2024年8月*
