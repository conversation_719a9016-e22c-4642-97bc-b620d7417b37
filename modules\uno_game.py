import random
import json
import time

class UnoCard:
    """UNO 卡牌类"""
    def __init__(self, color, value):
        self.color = color  # 红, 蓝, 绿, 黄, 黑(万能牌)
        self.value = value  # 0-9, Skip, Reverse, +2, 变色, +4变色
    
    def __str__(self):
        if self.color == "黑":
            return self.value
        return f"{self.color}{self.value}"
    
    def __eq__(self, other):
        return self.color == other.color and self.value == other.value
    
    def can_play_on(self, other_card, current_color=None):
        """判断是否可以在另一张牌上出牌"""
        # 万能牌可以在任何牌上出
        if self.color == "黑":
            return True
        
        # 如果当前有指定颜色（万能牌后），必须匹配指定颜色
        if current_color:
            return self.color == current_color
        
        # 普通情况：颜色或数字相同
        return self.color == other_card.color or self.value == other_card.value

class UnoGame:
    """UNO 游戏类"""
    def __init__(self):
        self.deck = []
        self.discard_pile = []
        self.players = {}  # {nick: {"hand": [], "uno_called": False}}
        self.turn_order = []
        self.current_player_index = 0
        self.direction = 1  # 1为正向，-1为反向
        self.current_color = None  # 万能牌指定的颜色
        self.game_started = False
        self.winner = None
        
    def create_deck(self):
        """创建完整的UNO牌组"""
        colors = ["红", "蓝", "绿", "黄"]
        
        # 数字牌：0各1张，1-9各2张
        for color in colors:
            self.deck.append(UnoCard(color, "0"))
            for value in range(1, 10):
                self.deck.append(UnoCard(color, str(value)))
                self.deck.append(UnoCard(color, str(value)))
        
        # 功能牌：每色各2张
        for color in colors:
            for _ in range(2):
                self.deck.append(UnoCard(color, "跳过"))
                self.deck.append(UnoCard(color, "反转"))
                self.deck.append(UnoCard(color, "+2"))
        
        # 万能牌：各4张
        for _ in range(4):
            self.deck.append(UnoCard("黑", "变色"))
            self.deck.append(UnoCard("黑", "+4变色"))
        
        random.shuffle(self.deck)
    
    def deal_cards(self):
        """发牌，每人7张"""
        for _ in range(7):
            for player in self.turn_order:
                if self.deck:
                    card = self.deck.pop()
                    self.players[player]["hand"].append(card)
        
        # 翻开第一张牌作为起始牌
        if self.deck:
            first_card = self.deck.pop()
            # 如果第一张是万能牌，重新抽取
            while first_card.color == "黑":
                self.deck.insert(0, first_card)
                random.shuffle(self.deck)
                first_card = self.deck.pop()
            
            self.discard_pile.append(first_card)
            self.current_color = first_card.color
    
    def add_player(self, nick):
        """添加玩家"""
        if nick not in self.players and len(self.players) < 8:
            self.players[nick] = {"hand": [], "uno_called": False}
            self.turn_order.append(nick)
            return True
        return False
    
    def remove_player(self, nick):
        """移除玩家"""
        if nick in self.players:
            del self.players[nick]
            if nick in self.turn_order:
                # 调整当前玩家索引
                if nick == self.get_current_player():
                    self.next_turn()
                player_index = self.turn_order.index(nick)
                if player_index < self.current_player_index:
                    self.current_player_index -= 1
                self.turn_order.remove(nick)
            return True
        return False
    
    def start_game(self):
        """开始游戏"""
        if len(self.players) >= 2:
            self.create_deck()
            self.deal_cards()
            self.game_started = True
            self.current_player_index = 0
            return True
        return False
    
    def get_current_player(self):
        """获取当前玩家"""
        if self.turn_order:
            return self.turn_order[self.current_player_index]
        return None
    
    def get_top_card(self):
        """获取弃牌堆顶部的牌"""
        if self.discard_pile:
            return self.discard_pile[-1]
        return None
    
    def next_turn(self):
        """切换到下一个玩家"""
        if self.turn_order:
            self.current_player_index = (self.current_player_index + self.direction) % len(self.turn_order)
    
    def reverse_direction(self):
        """反转出牌方向"""
        self.direction *= -1
    
    def draw_card(self, player_nick, count=1):
        """玩家摸牌"""
        if player_nick not in self.players:
            return []
        
        drawn_cards = []
        for _ in range(count):
            if not self.deck:
                # 牌堆空了，重新洗牌（保留弃牌堆顶部一张）
                if len(self.discard_pile) > 1:
                    top_card = self.discard_pile.pop()
                    self.deck = self.discard_pile[:]
                    self.discard_pile = [top_card]
                    random.shuffle(self.deck)
                else:
                    break  # 没有牌可摸了
            
            if self.deck:
                card = self.deck.pop()
                self.players[player_nick]["hand"].append(card)
                drawn_cards.append(card)
        
        return drawn_cards
    
    def play_card(self, player_nick, card_str, new_color=None):
        """玩家出牌"""
        if not self.game_started or self.winner:
            return False, "游戏未开始或已结束"
        
        if player_nick != self.get_current_player():
            return False, "不是你的回合"
        
        # 查找玩家手中的牌
        player_hand = self.players[player_nick]["hand"]
        card_to_play = None
        
        for card in player_hand:
            if str(card) == card_str:
                card_to_play = card
                break
        
        if not card_to_play:
            return False, "你没有这张牌"
        
        # 检查是否可以出牌
        top_card = self.get_top_card()
        if not card_to_play.can_play_on(top_card, self.current_color):
            return False, "这张牌不能出"
        
        # 出牌
        player_hand.remove(card_to_play)
        self.discard_pile.append(card_to_play)
        
        # 重置UNO状态
        self.players[player_nick]["uno_called"] = False
        
        # 处理特殊牌效果
        effect_msg = self.handle_card_effect(card_to_play, new_color)
        
        # 检查是否获胜
        if len(player_hand) == 0:
            self.winner = player_nick
            return True, f"出牌成功！{effect_msg}🎉 {player_nick} 获胜！"
        
        # 检查是否需要喊UNO
        if len(player_hand) == 1 and not self.players[player_nick]["uno_called"]:
            # 自动惩罚：摸2张牌
            self.draw_card(player_nick, 2)
            effect_msg += " | ⚠️ 未喊UNO，摸2张牌作为惩罚"
        
        return True, f"出牌成功！{effect_msg}"
    
    def handle_card_effect(self, card, new_color=None):
        """处理特殊牌效果"""
        effect_msg = ""
        
        if card.value == "跳过":
            self.next_turn()
            skipped_player = self.get_current_player()
            effect_msg = f"{skipped_player} 被跳过"
            
        elif card.value == "反转":
            self.reverse_direction()
            effect_msg = "出牌方向反转"
            
        elif card.value == "+2":
            self.next_turn()
            target_player = self.get_current_player()
            self.draw_card(target_player, 2)
            effect_msg = f"{target_player} 摸2张牌并跳过"
            
        elif card.value == "变色":
            if new_color in ["红", "蓝", "绿", "黄"]:
                self.current_color = new_color
                effect_msg = f"颜色变为{new_color}"
            else:
                self.current_color = "红"  # 默认红色
                effect_msg = "颜色变为红"
                
        elif card.value == "+4变色":
            self.next_turn()
            target_player = self.get_current_player()
            self.draw_card(target_player, 4)
            if new_color in ["红", "蓝", "绿", "黄"]:
                self.current_color = new_color
                effect_msg = f"{target_player} 摸4张牌并跳过，颜色变为{new_color}"
            else:
                self.current_color = "红"
                effect_msg = f"{target_player} 摸4张牌并跳过，颜色变为红"
        else:
            # 普通数字牌
            self.current_color = card.color
        
        # 切换到下一个玩家（除非已经在特殊效果中切换过）
        if card.value not in ["跳过", "+2", "+4变色"]:
            self.next_turn()
        
        return effect_msg
    
    def call_uno(self, player_nick):
        """玩家喊UNO"""
        if player_nick in self.players:
            if len(self.players[player_nick]["hand"]) == 1:
                self.players[player_nick]["uno_called"] = True
                return True, "UNO！"
            else:
                return False, "你的手牌不是1张，无法喊UNO"
        return False, "玩家不存在"
    
    def get_game_status(self):
        """获取游戏状态"""
        if not self.game_started:
            return "游戏未开始"
        
        if self.winner:
            return f"游戏结束，{self.winner} 获胜！"
        
        top_card = self.get_top_card()
        current_player = self.get_current_player()
        
        status = f"当前牌: {top_card}"
        if self.current_color and top_card.color == "黑":
            status += f" (颜色: {self.current_color})"
        
        status += f"\n当前玩家: {current_player}"
        status += f"\n出牌方向: {'正向' if self.direction == 1 else '反向'}"
        
        # 显示所有玩家手牌数量
        status += "\n玩家手牌数:"
        for player in self.turn_order:
            hand_count = len(self.players[player]["hand"])
            uno_status = " (UNO!)" if self.players[player]["uno_called"] else ""
            status += f" {player}:{hand_count}张{uno_status}"
        
        return status
    
    def get_player_hand(self, player_nick):
        """获取玩家手牌"""
        if player_nick in self.players:
            hand = self.players[player_nick]["hand"]
            return [str(card) for card in hand]
        return []
