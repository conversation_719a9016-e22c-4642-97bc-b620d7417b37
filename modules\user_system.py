#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户系统管理
"""

import json
import os
import time

class UserSystem:
    """用户系统管理器"""

    def __init__(self, config_file="config/user_config.json", user_data_file="userdata/user_data.json"):
        self.config_file = config_file
        self.user_data_file = user_data_file
        self.default_config = {
            "known_users": [],
            "welcome_message": "欢迎来到聊天室！输入 help 查看机器人功能。"
        }
        self.default_user_data = {
            "users": {},
            "shop_items": {
                "1": {
                    "id": "custom_welcome",
                    "name": "自定义欢迎语",
                    "description": "设置专属的个人欢迎语",
                    "price": 50,
                    "update_price": 1
                },
                "2": {
                    "id": "checkin_double_card",
                    "name": "签到金币翻倍卡",
                    "description": "下次签到获得的金币翻倍",
                    "price": 120
                },
                "3": {
                    "id": "idiom_skip_card",
                    "name": "成语接龙跳过卡",
                    "description": "在成语接龙游戏中跳过一次轮次",
                    "price": 40
                },
                "4": {
                    "id": "wordle_hint_card",
                    "name": "Wordle提示卡",
                    "description": "在Wordle游戏中获得一个字母位置提示",
                    "price": 30
                },
                "5": {
                    "id": "mystery_box",
                    "name": "神秘礼盒",
                    "description": "随机获得奖励或空奖励 (50%概率)",
                    "price": 80
                }
            },
            "purchase_records": {},  # 购买记录
            "rankings": {
                "wordle_time": {},  # Wordle计时排行榜 {user_key: best_time}
                "idiom_rounds": {}  # 成语接龙轮次排行榜 {user_key: max_rounds}
            },
            "daily_checkin": {
                "today": "",  # 今日日期 YYYY-MM-DD
                "checkin_list": [],  # 今日签到列表 [{"user_key": "", "time": timestamp, "rank": 1}, ...]
                "checkin_history": {}  # 用户签到历史 {user_key: {"last_checkin": "YYYY-MM-DD", "consecutive_days": 0, "total_days": 0}}
            }
        }
        self.config = self.load_config()
        self.user_data = self.load_user_data()
    
    def load_config(self):
        """加载用户配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 确保所有必要的键都存在
                    for key, value in self.default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载用户配置失败: {e}")
            return self.default_config.copy()

    def load_user_data(self):
        """加载用户数据"""
        try:
            # 确保userdata目录存在
            os.makedirs(os.path.dirname(self.user_data_file), exist_ok=True)

            if os.path.exists(self.user_data_file):
                with open(self.user_data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 确保所有必要的键都存在
                    for key, value in self.default_user_data.items():
                        if key not in data:
                            data[key] = value
                    return data
            else:
                return self.default_user_data.copy()
        except Exception as e:
            print(f"加载用户数据失败: {e}")
            return self.default_user_data.copy()
    
    def save_config(self):
        """保存用户配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存用户配置失败: {e}")
            return False

    def save_user_data(self):
        """保存用户数据"""
        try:
            # 确保userdata目录存在
            os.makedirs(os.path.dirname(self.user_data_file), exist_ok=True)

            with open(self.user_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存用户数据失败: {e}")
            return False
    
    def is_known_user(self, username):
        """检查是否为已知用户"""
        return username in self.config["known_users"]
    
    def add_user(self, username):
        """添加新用户"""
        if username not in self.config["known_users"]:
            self.config["known_users"].append(username)
            self.save_config()
            return True
        return False
    
    def get_welcome_message(self):
        """获取欢迎消息"""
        return self.config["welcome_message"]
    
    def set_welcome_message(self, message):
        """设置欢迎消息"""
        self.config["welcome_message"] = message
        return self.save_config()
    
    def get_user_count(self):
        """获取已知用户数量"""
        return len(self.config["known_users"])
    
    def get_user_list(self):
        """获取用户列表"""
        return self.config["known_users"].copy()
    
    def remove_user(self, username):
        """移除用户"""
        if username in self.config["known_users"]:
            self.config["known_users"].remove(username)
            self.save_config()
            return True
        return False

    # 用户身份验证功能
    def get_user_key(self, username, trip):
        """生成用户唯一标识"""
        return f"{username}#{trip}" if trip else username

    def verify_user_identity(self, username, trip):
        """验证用户身份"""
        user_key = self.get_user_key(username, trip)
        if user_key not in self.user_data["users"]:
            return False, "用户身份验证失败，请确保使用正确的昵称和识别码"
        return True, user_key

    def init_user_data(self, username, trip):
        """初始化用户数据"""
        user_key = self.get_user_key(username, trip)
        if user_key not in self.user_data["users"]:
            self.user_data["users"][user_key] = {
                "username": username,
                "trip": trip,
                "coins": 0,
                "custom_welcome": None,
                "custom_welcome_enabled": False,
                "wordle_best_time": None,
                "idiom_max_rounds": 0,
                "inventory": {
                    "checkin_double_card": 0,
                    "idiom_skip_card": 0,
                    "wordle_hint_card": 0
                },
                "stock_portfolio": {}  # 股票投资组合 {股票代码: 持股数量}
            }
            self.save_user_data()
        return user_key

    # 金币系统功能
    def get_user_coins(self, username, trip=None):
        """获取用户金币数量"""
        user_key = self.init_user_data(username, trip)
        return self.user_data["users"][user_key]["coins"]

    def add_coins(self, username, amount, trip=None):
        """给用户添加金币"""
        user_key = self.init_user_data(username, trip)
        self.user_data["users"][user_key]["coins"] += amount
        self.save_user_data()
        return self.user_data["users"][user_key]["coins"]

    def spend_coins(self, username, amount, trip=None):
        """用户花费金币"""
        user_key = self.init_user_data(username, trip)
        current_coins = self.user_data["users"][user_key]["coins"]
        if current_coins >= amount:
            self.user_data["users"][user_key]["coins"] -= amount
            self.save_user_data()
            return True
        return False
    
    def migrate_from_old_files(self, userhi_file="userhi.txt", wel_file="wel.txt"):
        """从旧文件迁移数据"""
        migrated = False
        
        # 迁移用户列表
        try:
            if os.path.exists(userhi_file):
                with open(userhi_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        old_users = content.split(',')
                        for user in old_users:
                            user = user.strip()
                            if user and user not in self.config["known_users"]:
                                self.config["known_users"].append(user)
                                migrated = True
        except Exception as e:
            print(f"迁移用户列表失败: {e}")
        
        # 迁移欢迎消息
        try:
            if os.path.exists(wel_file):
                with open(wel_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content and content != self.config["welcome_message"]:
                        self.config["welcome_message"] = content
                        migrated = True
        except Exception as e:
            print(f"迁移欢迎消息失败: {e}")
        
        if migrated:
            self.save_config()
            print("✅ 已从旧文件迁移数据到用户系统")
        
        return migrated

    # 商店系统功能
    def get_shop_items(self):
        """获取商店物品列表"""
        return self.user_data["shop_items"]

    def buy_custom_welcome(self, username, welcome_message, trip=None):
        """购买自定义欢迎语（兼容旧接口）"""
        return self.buy_item_by_id(username, trip, "1", welcome_message)

    def update_custom_welcome(self, username, welcome_message, trip=None):
        """更新自定义欢迎语"""
        user_key = self.init_user_data(username, trip)
        if not self.user_data["users"][user_key].get("custom_welcome"):
            return False, "你还没有购买自定义欢迎语功能"

        shop_items = self.get_shop_items()
        update_price = shop_items["1"]["update_price"]

        if self.spend_coins(username, update_price, trip):
            self.user_data["users"][user_key]["custom_welcome"] = welcome_message
            self.save_user_data()
            return True, f"成功更新自定义欢迎语！花费 {update_price} 金币"
        else:
            current_coins = self.get_user_coins(username, trip)
            return False, f"金币不足！需要 {update_price} 金币，当前拥有 {current_coins} 金币"

    def toggle_custom_welcome(self, username, enabled, trip=None):
        """开启/关闭自定义欢迎语"""
        user_key = self.init_user_data(username, trip)
        if not self.user_data["users"][user_key].get("custom_welcome"):
            return False, "你还没有购买自定义欢迎语功能"

        self.user_data["users"][user_key]["custom_welcome_enabled"] = enabled
        self.save_user_data()
        status = "开启" if enabled else "关闭"
        return True, f"已{status}自定义欢迎语"

    def get_user_welcome_message(self, username, trip=None):
        """获取用户的欢迎消息（如果有自定义的话）"""
        user_key = self.get_user_key(username, trip)
        if (user_key in self.user_data["users"] and
            self.user_data["users"][user_key].get("custom_welcome_enabled", False) and
            self.user_data["users"][user_key].get("custom_welcome")):
            return self.user_data["users"][user_key]["custom_welcome"]
        return self.config["welcome_message"]

    # 排名系统功能
    def get_coin_rankings(self, limit=10):
        """获取金币排行榜"""
        users_with_coins = []
        for user_key, user_data in self.user_data["users"].items():
            coins = user_data.get("coins", 0)
            username = user_data.get("username", user_key)
            if coins > 0:  # 只显示有金币的用户
                users_with_coins.append((username, coins))

        # 按金币数量降序排序
        users_with_coins.sort(key=lambda x: x[1], reverse=True)

        # 返回前N名
        return users_with_coins[:limit]

    def get_user_rank(self, username, trip=None):
        """获取用户的排名"""
        target_user_key = self.get_user_key(username, trip)
        users_with_coins = []

        for user_key, user_data in self.user_data["users"].items():
            coins = user_data.get("coins", 0)
            display_name = user_data.get("username", user_key)
            users_with_coins.append((user_key, display_name, coins))

        # 按金币数量降序排序
        users_with_coins.sort(key=lambda x: x[2], reverse=True)

        # 查找用户排名
        for rank, (user_key, display_name, coins) in enumerate(users_with_coins, 1):
            if user_key == target_user_key:
                return rank, coins

        # 用户不存在或没有金币记录
        return None, 0

    # 排行榜记录功能
    def record_wordle_time(self, username, trip, time_seconds):
        """记录Wordle完成时间"""
        user_key = self.init_user_data(username, trip)
        current_best = self.user_data["users"][user_key].get("wordle_best_time")

        if current_best is None or time_seconds < current_best:
            self.user_data["users"][user_key]["wordle_best_time"] = time_seconds
            self.user_data["rankings"]["wordle_time"][user_key] = time_seconds
            self.save_user_data()
            return True, time_seconds  # 新纪录
        return False, current_best  # 未破纪录

    def record_idiom_rounds(self, username, trip, rounds):
        """记录成语接龙轮次"""
        user_key = self.init_user_data(username, trip)
        current_max = self.user_data["users"][user_key].get("idiom_max_rounds", 0)

        if rounds > current_max:
            self.user_data["users"][user_key]["idiom_max_rounds"] = rounds
            self.user_data["rankings"]["idiom_rounds"][user_key] = rounds
            self.save_user_data()
            return True, rounds  # 新纪录
        return False, current_max  # 未破纪录

    def get_wordle_rankings(self, limit=10):
        """获取Wordle计时排行榜（时间越短越好）"""
        rankings = []
        for user_key, time_seconds in self.user_data["rankings"]["wordle_time"].items():
            if user_key in self.user_data["users"]:
                username = self.user_data["users"][user_key]["username"]
                rankings.append((username, time_seconds))

        # 按时间升序排序（时间越短排名越高）
        rankings.sort(key=lambda x: x[1])
        return rankings[:limit]

    def get_idiom_rankings(self, limit=10):
        """获取成语接龙轮次排行榜（轮次越多越好）"""
        rankings = []
        for user_key, rounds in self.user_data["rankings"]["idiom_rounds"].items():
            if user_key in self.user_data["users"] and rounds > 0:
                username = self.user_data["users"][user_key]["username"]
                rankings.append((username, rounds))

        # 按轮次降序排序（轮次越多排名越高）
        rankings.sort(key=lambda x: x[1], reverse=True)
        return rankings[:limit]

    # 购买记录和流水单号功能
    def generate_order_id(self):
        """生成随机流水单号"""
        import random
        import string
        import time

        # 格式：ORD + 时间戳后6位 + 4位随机字符
        timestamp = str(int(time.time()))[-6:]
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        return f"ORD{timestamp}{random_chars}"

    def buy_item_by_id(self, username, trip, item_id, *args):
        """通过商品ID购买物品"""
        # 验证用户身份
        verified, user_key = self.verify_user_identity(username, trip)
        if not verified:
            return False, user_key, None

        # 检查商品是否存在
        if item_id not in self.user_data["shop_items"]:
            return False, f"商品 {item_id} 不存在", None

        item = self.user_data["shop_items"][item_id]
        price = item["price"]

        # 检查金币是否足够
        if not self.spend_coins(username, price, trip):
            current_coins = self.get_user_coins(username, trip)
            return False, f"金币不足！需要 {price} 金币，当前拥有 {current_coins} 金币", None

        # 生成流水单号
        order_id = self.generate_order_id()

        # 记录购买
        purchase_record = {
            "order_id": order_id,
            "username": username,
            "trip": trip,
            "user_key": user_key,
            "item_id": item_id,
            "item_name": item["name"],
            "price": price,
            "timestamp": time.time(),
            "datetime": time.strftime("%Y-%m-%d %H:%M:%S"),
            "args": args
        }

        # 保存购买记录
        if user_key not in self.user_data["purchase_records"]:
            self.user_data["purchase_records"][user_key] = []
        self.user_data["purchase_records"][user_key].append(purchase_record)

        # 执行具体的购买逻辑
        if item_id == "1" or item["id"] == "custom_welcome":
            if len(args) > 0:
                welcome_message = " ".join(args)
                self.user_data["users"][user_key]["custom_welcome"] = welcome_message
                self.user_data["users"][user_key]["custom_welcome_enabled"] = True
                self.save_user_data()
                return True, f"成功购买 {item['name']}！\n流水单号: {order_id}\n花费: {price} 金币", order_id
            else:
                # 退还金币
                self.add_coins(username, price, trip)
                return False, "请提供欢迎语内容", None

        elif item_id == "2" or item["id"] == "checkin_double_card":
            # 签到金币翻倍卡
            self.user_data["users"][user_key]["inventory"]["checkin_double_card"] += 1
            self.save_user_data()
            return True, f"成功购买 {item['name']}！\n流水单号: {order_id}\n花费: {price} 金币\n已添加到背包", order_id

        elif item_id == "3" or item["id"] == "idiom_skip_card":
            # 成语接龙跳过卡
            self.user_data["users"][user_key]["inventory"]["idiom_skip_card"] += 1
            self.save_user_data()
            return True, f"成功购买 {item['name']}！\n流水单号: {order_id}\n花费: {price} 金币\n已添加到背包", order_id

        elif item_id == "4" or item["id"] == "wordle_hint_card":
            # Wordle提示卡
            self.user_data["users"][user_key]["inventory"]["wordle_hint_card"] += 1
            self.save_user_data()
            return True, f"成功购买 {item['name']}！\n流水单号: {order_id}\n花费: {price} 金币\n已添加到背包", order_id

        elif item_id == "5" or item["id"] == "mystery_box":
            # 神秘礼盒
            reward_message = self.open_mystery_box(user_key)
            self.save_user_data()
            return True, f"成功购买 {item['name']}！\n流水单号: {order_id}\n花费: {price} 金币\n\n🎁 开启结果:\n{reward_message}", order_id

        self.save_user_data()
        return True, f"购买成功！流水单号: {order_id}", order_id

    def get_purchase_history(self, username, trip):
        """获取用户购买历史"""
        verified, user_key = self.verify_user_identity(username, trip)
        if not verified:
            return []

        return self.user_data["purchase_records"].get(user_key, [])

    def open_mystery_box(self, user_key):
        """开启神秘礼盒"""
        import random

        # 50%概率获得奖励
        if random.random() < 0.5:
            return "💔 很遗憾，这次什么都没有..."

        # 随机选择奖励类型
        rewards = [
            ("coins", 0.4),      # 40%概率获得金币
            ("double_card", 0.2), # 20%概率获得翻倍卡
            ("skip_card", 0.2),   # 20%概率获得跳过卡
            ("hint_card", 0.2)    # 20%概率获得提示卡
        ]

        # 如果用户没有自定义欢迎语，增加获得的概率
        if not self.user_data["users"][user_key].get("custom_welcome"):
            rewards.append(("welcome", 0.1))  # 10%概率获得欢迎语

        # 根据概率选择奖励
        rand = random.random()
        cumulative = 0
        selected_reward = "coins"  # 默认奖励

        for reward_type, probability in rewards:
            cumulative += probability
            if rand <= cumulative:
                selected_reward = reward_type
                break

        # 执行奖励
        if selected_reward == "coins":
            coins = random.randint(20, 200)
            self.user_data["users"][user_key]["coins"] += coins
            return f"🪙 恭喜获得 {coins} 金币！"

        elif selected_reward == "double_card":
            self.user_data["users"][user_key]["inventory"]["checkin_double_card"] += 1
            return "🎫 恭喜获得签到金币翻倍卡 x1！"

        elif selected_reward == "skip_card":
            self.user_data["users"][user_key]["inventory"]["idiom_skip_card"] += 1
            return "🎫 恭喜获得成语接龙跳过卡 x1！"

        elif selected_reward == "hint_card":
            self.user_data["users"][user_key]["inventory"]["wordle_hint_card"] += 1
            return "🎫 恭喜获得Wordle提示卡 x1！"

        elif selected_reward == "welcome":
            self.user_data["users"][user_key]["custom_welcome"] = "恭喜你获得了神秘礼盒的特殊奖励！"
            self.user_data["users"][user_key]["custom_welcome_enabled"] = True
            return "🎉 恭喜获得自定义欢迎语功能！已自动设置默认欢迎语，可使用 shop update welcome 修改"

        return "💔 很遗憾，这次什么都没有..."

    def get_user_inventory(self, username, trip=None):
        """获取用户背包"""
        user_key = self.init_user_data(username, trip)
        inventory = self.user_data["users"][user_key]["inventory"]

        items = []
        if inventory["checkin_double_card"] > 0:
            items.append(f"签到金币翻倍卡 x{inventory['checkin_double_card']}")
        if inventory["idiom_skip_card"] > 0:
            items.append(f"成语接龙跳过卡 x{inventory['idiom_skip_card']}")
        if inventory["wordle_hint_card"] > 0:
            items.append(f"Wordle提示卡 x{inventory['wordle_hint_card']}")

        return items

    def use_item(self, username, trip, item_type):
        """使用道具"""
        user_key = self.init_user_data(username, trip)
        inventory = self.user_data["users"][user_key]["inventory"]

        if item_type not in inventory or inventory[item_type] <= 0:
            return False, f"你没有 {item_type}"

        inventory[item_type] -= 1
        self.save_user_data()
        return True, f"成功使用 {item_type}"

    # 每日签到系统功能
    def get_today_date(self):
        """获取今日日期字符串"""
        import time
        return time.strftime("%Y-%m-%d")

    def reset_daily_checkin_if_needed(self):
        """如果需要的话重置每日签到数据"""
        today = self.get_today_date()
        if self.user_data["daily_checkin"]["today"] != today:
            self.user_data["daily_checkin"]["today"] = today
            self.user_data["daily_checkin"]["checkin_list"] = []
            self.save_user_data()

    def has_checked_in_today(self, username, trip=None):
        """检查用户今天是否已经签到"""
        user_key = self.get_user_key(username, trip)
        self.reset_daily_checkin_if_needed()

        for checkin in self.user_data["daily_checkin"]["checkin_list"]:
            if checkin["user_key"] == user_key:
                return True
        return False

    def get_consecutive_days(self, username, trip=None):
        """获取用户连续签到天数"""
        user_key = self.get_user_key(username, trip)
        history = self.user_data["daily_checkin"]["checkin_history"].get(user_key, {})
        return history.get("consecutive_days", 0)

    def calculate_checkin_reward(self, rank, consecutive_days, has_double_card=False):
        """计算签到奖励"""
        # 基础奖励
        base_reward = 10

        # 排名奖励
        rank_bonus = 0
        if rank == 1:
            rank_bonus = 20
        elif rank == 2:
            rank_bonus = 15
        elif rank == 3:
            rank_bonus = 10

        # 连续签到奖励
        consecutive_bonus = 0
        if consecutive_days >= 30:
            consecutive_bonus = 50
        elif consecutive_days >= 15:
            consecutive_bonus = 30
        elif consecutive_days >= 7:
            consecutive_bonus = 15
        elif consecutive_days >= 3:
            consecutive_bonus = 5

        total_reward = base_reward + rank_bonus + consecutive_bonus

        # 翻倍卡效果
        if has_double_card:
            total_reward *= 2

        return total_reward, base_reward, rank_bonus, consecutive_bonus

    def daily_checkin(self, username, trip=None):
        """用户每日签到"""
        import time

        user_key = self.init_user_data(username, trip)

        # 检查是否已经签到
        if self.has_checked_in_today(username, trip):
            return False, "你今天已经签到过了！", None

        # 重置每日数据（如果需要）
        self.reset_daily_checkin_if_needed()

        # 检查是否有翻倍卡
        has_double_card = self.user_data["users"][user_key]["inventory"]["checkin_double_card"] > 0

        # 计算排名（当前签到人数 + 1）
        current_rank = len(self.user_data["daily_checkin"]["checkin_list"]) + 1

        # 获取连续签到天数
        today = self.get_today_date()
        yesterday = self.get_yesterday_date()

        history = self.user_data["daily_checkin"]["checkin_history"].get(user_key, {
            "last_checkin": "",
            "consecutive_days": 0,
            "total_days": 0
        })

        # 计算连续天数
        if history["last_checkin"] == yesterday:
            consecutive_days = history["consecutive_days"] + 1
        elif history["last_checkin"] == today:
            consecutive_days = history["consecutive_days"]  # 今天已经签到过（理论上不会到这里）
        else:
            consecutive_days = 1  # 重新开始计算

        # 计算奖励
        total_reward, base_reward, rank_bonus, consecutive_bonus = self.calculate_checkin_reward(
            current_rank, consecutive_days, has_double_card
        )

        # 添加金币
        new_total = self.add_coins(username, total_reward, trip)

        # 如果使用了翻倍卡，消耗它
        if has_double_card:
            self.user_data["users"][user_key]["inventory"]["checkin_double_card"] -= 1

        # 记录签到
        checkin_record = {
            "user_key": user_key,
            "username": username,
            "time": time.time(),
            "rank": current_rank,
            "reward": total_reward,
            "used_double_card": has_double_card
        }
        self.user_data["daily_checkin"]["checkin_list"].append(checkin_record)

        # 更新签到历史
        self.user_data["daily_checkin"]["checkin_history"][user_key] = {
            "last_checkin": today,
            "consecutive_days": consecutive_days,
            "total_days": history["total_days"] + 1
        }

        self.save_user_data()

        # 构建返回消息
        reward_details = {
            "total_reward": total_reward,
            "base_reward": base_reward,
            "rank_bonus": rank_bonus,
            "consecutive_bonus": consecutive_bonus,
            "rank": current_rank,
            "consecutive_days": consecutive_days,
            "new_total": new_total,
            "used_double_card": has_double_card
        }

        return True, "签到成功！", reward_details

    def get_yesterday_date(self):
        """获取昨天的日期字符串"""
        import time
        import datetime
        today = datetime.date.today()
        yesterday = today - datetime.timedelta(days=1)
        return yesterday.strftime("%Y-%m-%d")

    def get_daily_checkin_rankings(self, limit=10):
        """获取今日签到排行榜"""
        self.reset_daily_checkin_if_needed()

        rankings = []
        for checkin in self.user_data["daily_checkin"]["checkin_list"]:
            rankings.append({
                "rank": checkin["rank"],
                "username": checkin["username"],
                "time": checkin["time"],
                "reward": checkin["reward"],
                "used_double_card": checkin.get("used_double_card", False)
            })

        # 按排名排序
        rankings.sort(key=lambda x: x["rank"])
        return rankings[:limit]
    
    # 股票系统功能
    def get_user_portfolio(self, username, trip=None):
        """获取用户股票投资组合"""
        user_key = self.init_user_data(username, trip)
        return self.user_data["users"][user_key].get("stock_portfolio", {})
    
    def buy_stock(self, username, trip, symbol, quantity, cost):
        """购买股票"""
        user_key = self.init_user_data(username, trip)
        
        # 扣除金币
        if not self.spend_coins(username, cost, trip):
            return False
        
        # 更新投资组合
        if "stock_portfolio" not in self.user_data["users"][user_key]:
            self.user_data["users"][user_key]["stock_portfolio"] = {}
        
        portfolio = self.user_data["users"][user_key]["stock_portfolio"]
        portfolio[symbol] = portfolio.get(symbol, 0) + quantity
        
        self.save_user_data()
        return True
    
    def sell_stock(self, username, trip, symbol, quantity, revenue):
        """卖出股票"""
        user_key = self.init_user_data(username, trip)
        
        # 检查持股
        portfolio = self.user_data["users"][user_key].get("stock_portfolio", {})
        if symbol not in portfolio or portfolio[symbol] < quantity:
            return False
        
        # 更新持股
        portfolio[symbol] -= quantity
        if portfolio[symbol] == 0:
            del portfolio[symbol]
        
        # 增加金币
        self.add_coins(username, revenue, trip)
        
        self.save_user_data()
        return True

# 全局用户系统实例
user_system = UserSystem()

def initialize_user_system():
    """初始化用户系统"""
    # 尝试从旧文件迁移
    user_system.migrate_from_old_files()
    return user_system
