#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实炒股游戏模块
使用虚拟金币进行股票交易
"""

import json
import time
import random
from typing import Dict, List, Optional, Tuple

class StockGame:
    """股票交易游戏类"""
    
    def __init__(self):
        # 支持的股票列表（使用数字代码简化操作）
        self.stocks = {
            # 科技股
            "1": {"name": "苹果公司", "base_price": 180.0, "volatility": 0.02},
            "2": {"name": "微软公司", "base_price": 350.0, "volatility": 0.018},
            "3": {"name": "谷歌母公司", "base_price": 140.0, "volatility": 0.025},
            "4": {"name": "特斯拉", "base_price": 250.0, "volatility": 0.04},
            "5": {"name": "英伟达", "base_price": 800.0, "volatility": 0.035},
            "6": {"name": "Meta平台", "base_price": 320.0, "volatility": 0.03},
            
            # 中国公司股票
            "7": {"name": "阿里巴巴", "base_price": 85.0, "volatility": 0.03},
            "8": {"name": "百度", "base_price": 110.0, "volatility": 0.025},
            "9": {"name": "京东", "base_price": 25.0, "volatility": 0.028},
            "10": {"name": "腾讯控股", "base_price": 38.0, "volatility": 0.025},
            "11": {"name": "拼多多", "base_price": 120.0, "volatility": 0.035},
            "12": {"name": "蔚来汽车", "base_price": 8.0, "volatility": 0.05},
            
            # 其他知名公司
            "13": {"name": "亚马逊", "base_price": 150.0, "volatility": 0.022},
            "14": {"name": "奈飞", "base_price": 450.0, "volatility": 0.03},
            "15": {"name": "AMD", "base_price": 140.0, "volatility": 0.035}
        }
        
        # 当前股价（模拟实时价格）
        self.current_prices = {}
        self.price_history = {}
        self.last_update = 0
        
        # 初始化股价
        self._initialize_prices()
    
    def _initialize_prices(self):
        """初始化股票价格"""
        for symbol, info in self.stocks.items():
            # 基准价格上下浮动10%作为初始价格
            initial_price = info["base_price"] * (0.9 + random.random() * 0.2)
            self.current_prices[symbol] = round(initial_price, 2)
            self.price_history[symbol] = [initial_price]
    
    def _update_prices(self):
        """更新股票价格（模拟市场波动）"""
        current_time = time.time()
        
        # 每30秒更新一次价格
        if current_time - self.last_update < 30:
            return
        
        self.last_update = current_time
        
        for symbol, info in self.stocks.items():
            current_price = self.current_prices[symbol]
            volatility = info["volatility"]
            
            # 使用随机游走模型模拟价格变动
            # 价格变动 = 当前价格 * 波动率 * 随机因子
            change_factor = random.gauss(0, volatility)  # 正态分布随机数
            new_price = current_price * (1 + change_factor)
            
            # 确保价格不会过于极端（不低于基准价格的50%，不高于200%）
            min_price = info["base_price"] * 0.5
            max_price = info["base_price"] * 2.0
            new_price = max(min_price, min(max_price, new_price))
            
            self.current_prices[symbol] = round(new_price, 2)
            
            # 保存价格历史（最多保存100个价格点）
            if len(self.price_history[symbol]) >= 100:
                self.price_history[symbol].pop(0)
            self.price_history[symbol].append(new_price)
    
    def get_stock_list(self) -> str:
        """获取股票列表"""
        self._update_prices()
        
        stock_list = "📈 **可交易股票列表**:\n\n"
        
        for symbol, info in self.stocks.items():
            current_price = self.current_prices[symbol]
            
            # 计算涨跌幅（与前一个价格比较）
            if len(self.price_history[symbol]) > 1:
                prev_price = self.price_history[symbol][-2]
                change = current_price - prev_price
                change_pct = (change / prev_price) * 100
                
                if change > 0:
                    change_str = f"📈 +${change:.2f} (+{change_pct:.2f}%)"
                elif change < 0:
                    change_str = f"📉 ${change:.2f} ({change_pct:.2f}%)"
                else:
                    change_str = "➡️ $0.00 (0.00%)"
            else:
                change_str = "➡️ $0.00 (0.00%)"
            
            stock_list += f"`{symbol}` **{info['name']}**\n"
            stock_list += f"   💰 ${current_price:.2f} {change_str}\n\n"
        
        stock_list += "💡 简化指令:\n"
        stock_list += "`get <代码> <数量>` - 买入股票 (如: get 1 10)\n"
        stock_list += "`sell <代码> <数量>` - 卖出股票 (如: sell 1 5)\n"
        stock_list += "`portfolio` - 查看投资组合\n"
        stock_list += "`price <代码>` - 查看股票详情 (如: price 1)"
        
        return stock_list
    
    def get_stock_price(self, symbol: str) -> Optional[Tuple[str, float, float]]:
        """获取单只股票价格信息"""
        if symbol not in self.stocks:
            return None
        
        self._update_prices()
        current_price = self.current_prices[symbol]
        
        # 计算涨跌幅
        change_pct = 0.0
        if len(self.price_history[symbol]) > 1:
            prev_price = self.price_history[symbol][-2]
            change_pct = ((current_price - prev_price) / prev_price) * 100
        
        return self.stocks[symbol]["name"], current_price, change_pct
    
    def buy_stock(self, user_coins: int, symbol: str, quantity: int) -> Tuple[bool, str, int]:
        """买入股票"""
        if symbol not in self.stocks:
            return False, f"股票代码 {symbol} 不存在", 0
        
        if quantity <= 0:
            return False, "购买数量必须大于0", 0
        
        self._update_prices()
        current_price = self.current_prices[symbol]
        total_cost = int(current_price * quantity)  # 转换为整数金币
        
        if user_coins < total_cost:
            return False, f"金币不足！需要 {total_cost} 金币，当前拥有 {user_coins} 金币", 0
        
        stock_name = self.stocks[symbol]["name"]
        return True, f"成功买入 {quantity} 股 {stock_name}({symbol})，单价 ${current_price:.2f}，总计花费 {total_cost} 金币", total_cost
    
    def sell_stock(self, portfolio: Dict[str, int], symbol: str, quantity: int) -> Tuple[bool, str, int]:
        """卖出股票"""
        if symbol not in self.stocks:
            return False, f"股票代码 {symbol} 不存在", 0
        
        if quantity <= 0:
            return False, "卖出数量必须大于0", 0
        
        if symbol not in portfolio or portfolio[symbol] < quantity:
            current_holding = portfolio.get(symbol, 0)
            return False, f"持股不足！当前持有 {current_holding} 股，无法卖出 {quantity} 股", 0
        
        self._update_prices()
        current_price = self.current_prices[symbol]
        total_revenue = int(current_price * quantity)  # 转换为整数金币
        
        stock_name = self.stocks[symbol]["name"]
        return True, f"成功卖出 {quantity} 股 {stock_name}({symbol})，单价 ${current_price:.2f}，总计获得 {total_revenue} 金币", total_revenue
    
    def get_portfolio_value(self, portfolio: Dict[str, int]) -> Tuple[int, str]:
        """计算投资组合总价值"""
        if not portfolio:
            return 0, "📊 **投资组合**: 暂无持股\n\n💡 使用 `stock list` 查看可交易股票"
        
        self._update_prices()
        total_value = 0
        portfolio_text = "📊 **投资组合**:\n\n"
        
        for symbol, quantity in portfolio.items():
            if symbol in self.stocks and quantity > 0:
                current_price = self.current_prices[symbol]
                stock_value = current_price * quantity
                total_value += int(stock_value)
                
                stock_name = self.stocks[symbol]["name"]
                portfolio_text += f"`{symbol}` **{stock_name}**\n"
                portfolio_text += f"   持股: {quantity} 股 × ${current_price:.2f} = {int(stock_value)} 金币\n\n"
        
        portfolio_text += f"💰 **投资组合总价值**: {total_value} 金币"
        return total_value, portfolio_text
    
    def get_stock_detail(self, symbol: str) -> Optional[str]:
        """获取股票详细信息"""
        symbol = symbol.upper()
        if symbol not in self.stocks:
            return None
        
        self._update_prices()
        stock_info = self.stocks[symbol]
        current_price = self.current_prices[symbol]
        
        # 计算涨跌幅和涨跌额
        change = 0.0
        change_pct = 0.0
        if len(self.price_history[symbol]) > 1:
            prev_price = self.price_history[symbol][-2]
            change = current_price - prev_price
            change_pct = (change / prev_price) * 100
        
        # 计算今日最高价和最低价（从最近10个价格点）
        recent_prices = self.price_history[symbol][-10:]
        day_high = max(recent_prices)
        day_low = min(recent_prices)
        
        detail_text = f"📈 **{stock_info['name']} ({symbol})**\n\n"
        detail_text += f"💰 **当前价格**: ${current_price:.2f}\n"
        
        if change > 0:
            detail_text += f"📈 **涨跌**: +${change:.2f} (+{change_pct:.2f}%)\n"
        elif change < 0:
            detail_text += f"📉 **涨跌**: ${change:.2f} ({change_pct:.2f}%)\n"
        else:
            detail_text += f"➡️ **涨跌**: $0.00 (0.00%)\n"
        
        detail_text += f"🔺 **今日最高**: ${day_high:.2f}\n"
        detail_text += f"🔻 **今日最低**: ${day_low:.2f}\n"
        detail_text += f"📊 **波动率**: {stock_info['volatility']*100:.1f}%\n\n"
        detail_text += f"💡 使用 `stock get {symbol} <数量>` 买入股票"
        
        return detail_text

# 全局股票游戏实例
stock_game = StockGame()