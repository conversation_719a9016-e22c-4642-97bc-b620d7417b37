{"timestamp": 1754284513.233532, "datetime": "2025-08-04 13:15:13", "room": "lounges", "nick": "BoB_O64W", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754284526.1525352, "datetime": "2025-08-04 13:15:26", "room": "lounges", "nick": "BoB_O64W", "trip": "BuR9sE", "text": "su你好鸭！希望你在这里玩得开心！", "cmd": "chat"}
{"timestamp": 1754284528.7160559, "datetime": "2025-08-04 13:15:28", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754284542.905039, "datetime": "2025-08-04 13:15:42", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help ai", "cmd": "chat"}
{"timestamp": 1754284550.561156, "datetime": "2025-08-04 13:15:50", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help uno", "cmd": "chat"}
{"timestamp": 1754284739.8317733, "datetime": "2025-08-04 13:18:59", "room": "lounges", "nick": "BoB_WZBT", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754284743.0381806, "datetime": "2025-08-04 13:19:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754284758.498972, "datetime": "2025-08-04 13:19:18", "room": "lounges", "nick": "BoB_WZBT", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754284831.930983, "datetime": "2025-08-04 13:20:31", "room": "lounges", "nick": "BoB_WZBT", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754284843.207707, "datetime": "2025-08-04 13:20:43", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help uno", "cmd": "chat"}
{"timestamp": 1754284850.0002928, "datetime": "2025-08-04 13:20:49", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help wordle", "cmd": "chat"}
{"timestamp": 1754284862.4945507, "datetime": "2025-08-04 13:21:02", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help idiom", "cmd": "chat"}
{"timestamp": 1754284871.4397297, "datetime": "2025-08-04 13:21:11", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help coins", "cmd": "chat"}
{"timestamp": 1754284889.7376509, "datetime": "2025-08-04 13:21:29", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help shop", "cmd": "chat"}
{"timestamp": 1754284889.7390876, "datetime": "2025-08-04 13:21:29", "room": "lounges", "nick": "BoB_WZBT", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754284900.8111546, "datetime": "2025-08-04 13:21:40", "room": "lounges", "nick": "BoB_WZBT", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754285470.1504047, "datetime": "2025-08-04 13:31:10", "room": "lounges", "nick": "BoB_N84W", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754285471.0822656, "datetime": "2025-08-04 13:31:11", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754285523.9717476, "datetime": "2025-08-04 13:32:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help idiom", "cmd": "chat"}
{"timestamp": 1754285640.8115427, "datetime": "2025-08-04 13:34:00", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help wordle", "cmd": "chat"}
{"timestamp": 1754285795.588925, "datetime": "2025-08-04 13:36:35", "room": "lounges", "nick": "BoB_N84W", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754287012.804689, "datetime": "2025-08-04 13:56:52", "room": "lounges", "nick": "BoB", "trip": "", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754287018.5915911, "datetime": "2025-08-04 13:56:58", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754287036.3457575, "datetime": "2025-08-04 13:57:16", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help all", "cmd": "chat"}
{"timestamp": 1754287161.864392, "datetime": "2025-08-04 13:59:21", "room": "lounges", "nick": "BoB", "trip": "", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754287162.2103536, "datetime": "2025-08-04 13:59:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help idiom", "cmd": "chat"}
{"timestamp": 1754287185.9062924, "datetime": "2025-08-04 13:59:45", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom join", "cmd": "chat"}
{"timestamp": 1754287186.9856966, "datetime": "2025-08-04 13:59:46", "room": "lounges", "nick": "BoB", "trip": "", "text": "su 加入成语接龙游戏！当前玩家: 1人", "cmd": "chat"}
{"timestamp": 1754287200.4571643, "datetime": "2025-08-04 14:00:00", "room": "lounges", "nick": "BoB", "trip": "", "text": "欢迎欢迎！祝你今天过得愉快！", "cmd": "chat"}
{"timestamp": 1754287221.0994976, "datetime": "2025-08-04 14:00:21", "room": "lounges", "nick": "akwu", "trip": "NQPGdL", "text": "idiom join", "cmd": "chat"}
{"timestamp": 1754287222.1959255, "datetime": "2025-08-04 14:00:22", "room": "lounges", "nick": "BoB", "trip": "", "text": "akwu 加入成语接龙游戏！当前玩家: 2人", "cmd": "chat"}
{"timestamp": 1754287227.656142, "datetime": "2025-08-04 14:00:27", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom start", "cmd": "chat"}
{"timestamp": 1754287228.0565975, "datetime": "2025-08-04 14:00:28", "room": "lounges", "nick": "BoB", "trip": "", "text": "成语接龙开始！第一个成语是：违天逆理，请 su 接龙（需要以'li'音开头的成语）", "cmd": "chat"}
{"timestamp": 1754287252.3156083, "datetime": "2025-08-04 14:00:52", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "@BoB 给我一个用音li开头的成语", "cmd": "chat"}
{"timestamp": 1754287254.0044906, "datetime": "2025-08-04 14:00:54", "room": "lounges", "nick": "BoB", "trip": "", "text": "su，一个以“li”音开头的成语是“立竿见影”。这个成语的意思是做事迅速，立刻见到效果。如果你需要其他成语或更多的帮助，请告诉我！", "cmd": "chat", "custom_id": "709356", "updated_at": "2025-08-04 14:00:57"}
{"timestamp": 1754287258.0574176, "datetime": "2025-08-04 14:00:58", "room": "lounges", "nick": "BoB", "trip": "", "text": "⏰ su 超时被淘汰！", "cmd": "chat"}
{"timestamp": 1754287258.952197, "datetime": "2025-08-04 14:00:58", "room": "lounges", "nick": "BoB", "trip": "", "text": "🎉 成语接龙游戏结束！参与人数不足3人，无金币奖励", "cmd": "chat"}
{"timestamp": 1754287258.9561625, "datetime": "2025-08-04 14:00:58", "room": "lounges", "nick": "BoB", "trip": "", "text": "🎉 成语接龙游戏结束！参与人数不足3人，无金币奖励", "cmd": "chat"}
{"timestamp": 1754287274.1250806, "datetime": "2025-08-04 14:01:14", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom start", "cmd": "chat"}
{"timestamp": 1754287274.4266176, "datetime": "2025-08-04 14:01:14", "room": "lounges", "nick": "BoB", "trip": "", "text": "游戏已经开始了", "cmd": "chat"}
{"timestamp": 1754287284.5788229, "datetime": "2025-08-04 14:01:24", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom join", "cmd": "chat"}
{"timestamp": 1754287284.8613043, "datetime": "2025-08-04 14:01:24", "room": "lounges", "nick": "BoB", "trip": "", "text": "游戏已开始，无法加入", "cmd": "chat"}
{"timestamp": 1754287491.1256914, "datetime": "2025-08-04 14:04:51", "room": "lounges", "nick": "BoB", "trip": "", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754287493.2728245, "datetime": "2025-08-04 14:04:53", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank", "cmd": "chat"}
{"timestamp": 1754287493.9107308, "datetime": "2025-08-04 14:04:53", "room": "lounges", "nick": "BoB", "trip": "", "text": "请指定排行榜类型:\n`rank 1` - 金币排行榜\n`rank 2` - Wordle计时排行榜\n`rank 3` - 成语接龙排行榜\n`rank <用户名>` - 查看用户排名", "cmd": "chat"}
{"timestamp": 1754287498.4527493, "datetime": "2025-08-04 14:04:58", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank 2", "cmd": "chat"}
{"timestamp": 1754287498.6521037, "datetime": "2025-08-04 14:04:58", "room": "lounges", "nick": "BoB", "trip": "", "text": "暂无Wordle计时排行榜数据", "cmd": "chat"}
{"timestamp": 1754287502.008319, "datetime": "2025-08-04 14:05:02", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank 3", "cmd": "chat"}
{"timestamp": 1754287502.2113092, "datetime": "2025-08-04 14:05:02", "room": "lounges", "nick": "BoB", "trip": "", "text": "暂无成语接龙排行榜数据", "cmd": "chat"}
{"timestamp": 1754287504.4003336, "datetime": "2025-08-04 14:05:04", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank 1", "cmd": "chat"}
{"timestamp": 1754287505.0159185, "datetime": "2025-08-04 14:05:05", "room": "lounges", "nick": "BoB", "trip": "", "text": "🏆 金币排行榜 (前10名):\n🥇 1. TestUser - 910 金币", "cmd": "chat"}
{"timestamp": 1754287519.5552502, "datetime": "2025-08-04 14:05:19", "room": "lounges", "nick": "BoB", "trip": "", "text": "欢迎欢迎！su！让我们一起度过美好时光吧！", "cmd": "chat"}
{"timestamp": 1754287636.4347315, "datetime": "2025-08-04 14:07:16", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754287642.0705063, "datetime": "2025-08-04 14:07:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754287651.5935912, "datetime": "2025-08-04 14:07:31", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom start", "cmd": "chat"}
{"timestamp": 1754287651.8238988, "datetime": "2025-08-04 14:07:31", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "至少需要2个玩家才能开始游戏", "cmd": "chat"}
{"timestamp": 1754287658.3358004, "datetime": "2025-08-04 14:07:38", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom join", "cmd": "chat"}
{"timestamp": 1754287658.5621243, "datetime": "2025-08-04 14:07:38", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su 加入成语接龙游戏！当前玩家: 1人", "cmd": "chat"}
{"timestamp": 1754287662.4655092, "datetime": "2025-08-04 14:07:42", "room": "lounges", "nick": "akwu", "trip": "NQPGdL", "text": "idiom join", "cmd": "chat"}
{"timestamp": 1754287663.5907826, "datetime": "2025-08-04 14:07:43", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "akwu 加入成语接龙游戏！当前玩家: 2人", "cmd": "chat"}
{"timestamp": 1754287667.1920772, "datetime": "2025-08-04 14:07:47", "room": "lounges", "nick": "akwu", "trip": "NQPGdL", "text": "idiom start", "cmd": "chat"}
{"timestamp": 1754287667.7447624, "datetime": "2025-08-04 14:07:47", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "成语接龙开始！第一个成语是：海涯天角，请 su 接龙（需要以'jiao'音开头的成语）", "cmd": "chat"}
{"timestamp": 1754287681.5904064, "datetime": "2025-08-04 14:08:01", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "@BoB 给我一个jiao开头的成语", "cmd": "chat"}
{"timestamp": 1754287681.7907102, "datetime": "2025-08-04 14:08:01", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su，一个以“jiao”音开头的成语是“焦头烂额”。这个成语形容一个人面临困境、非常忙乱或痛苦的状态。如果你有其他需要了解的内容，请随时告诉我！", "cmd": "chat", "custom_id": "175173", "updated_at": "2025-08-04 14:08:06"}
{"timestamp": 1754287691.1618335, "datetime": "2025-08-04 14:08:11", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "焦头烂额", "cmd": "chat"}
{"timestamp": 1754287691.3980863, "datetime": "2025-08-04 14:08:11", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su 接龙成功：焦头烂额\n现在轮到 akwu，请接'e'音", "cmd": "chat"}
{"timestamp": 1754287714.1683404, "datetime": "2025-08-04 14:08:34", "room": "lounges", "nick": "akwu", "trip": "NQPGdL", "text": "恶心至极", "cmd": "chat"}
{"timestamp": 1754287715.2651443, "datetime": "2025-08-04 14:08:35", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "akwu 答错被淘汰！'恶心至极' 不是有效的成语\n🎉 游戏结束！su 获胜！", "cmd": "chat"}
{"timestamp": 1754287726.143453, "datetime": "2025-08-04 14:08:46", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coin", "cmd": "chat"}
{"timestamp": 1754287728.9273543, "datetime": "2025-08-04 14:08:48", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754287729.1577203, "datetime": "2025-08-04 14:08:49", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su 当前拥有 0 金币", "cmd": "chat"}
{"timestamp": 1754287761.3682895, "datetime": "2025-08-04 14:09:21", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754287771.5176954, "datetime": "2025-08-04 14:09:31", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754287771.7482219, "datetime": "2025-08-04 14:09:31", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 1 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754287841.2585452, "datetime": "2025-08-04 14:10:41", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "uno start", "cmd": "chat"}
{"timestamp": 1754287841.4840162, "datetime": "2025-08-04 14:10:41", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "开始失败，需要至少2个玩家", "cmd": "chat"}
{"timestamp": 1754287880.650356, "datetime": "2025-08-04 14:11:20", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wel 傻逼", "cmd": "chat"}
{"timestamp": 1754287880.8473616, "datetime": "2025-08-04 14:11:20", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su: 金币不足，更新欢迎语需要1金币", "cmd": "chat"}
{"timestamp": 1754287890.833952, "datetime": "2025-08-04 14:11:30", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754287891.0360909, "datetime": "2025-08-04 14:11:31", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 1 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754287893.5296805, "datetime": "2025-08-04 14:11:33", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "buy 1", "cmd": "chat"}
{"timestamp": 1754287893.7554786, "datetime": "2025-08-04 14:11:33", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "❌ su: 金币不足！需要 100 金币，当前拥有 0 金币", "cmd": "chat"}
{"timestamp": 1754287960.521902, "datetime": "2025-08-04 14:12:40", "room": "lounges", "nick": "akwu", "trip": "NQPGdL", "text": "@BoB Python有没有股票的API或者库，就是能获取某种股票的实时的情况的", "cmd": "chat"}
{"timestamp": 1754287960.7504156, "datetime": "2025-08-04 14:12:40", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "akwu，是的，Python有多种库和API可以用来获取股票的实时数据。以下是一些常用的库和API：\n\n1. **yfinance**：\n   - `yfinance` 是一个非常流行的库，可以方便地从 Yahoo Finance 获取股票数据。它支持获取历史数据、实时数据、信息等。\n   - 安装方法：\n     ```bash\n     pip install yfinance\n     ```\n   - 使用示例：\n     ```python\n     import yfinance as yf\n     \n     stock = yf.Ticker('AAPL')  # 以苹果公司为例\n     print(stock.history(period='1d'))  # 获取过去一天的历史数据\n     print(stock.info)  # 获取更多的信息\n     ```\n\n2. **Alpha Vantage**：\n   - Alpha Vantage 提供了免费的 API，可以用来获取股票的实时数据、历史数据以及各种技术指标等。\n   - 需要注册获取一个 API 密钥。\n   - 安装方法：\n     ```bash\n     pip install alpha_vantage\n     ```\n   - 使用示例：\n     ```python\n     from alpha_vantage.timeseries import TimeSeries\n\n     ts = TimeSeries(key='YOUR_API_KEY', output_format='pandas')\n     data, metadata = ts.get_quote_endpoint(symbol='AAPL')\n     print(data)\n     ```\n\n3. **IEX Cloud**：\n   - IEX Cloud 提供了丰富的金融数据 API，包括股票的实时价格、历史数据等。需要注册并获取 API 密钥。\n   - 使用示例：\n     ```python\n     import requests\n\n     url = 'https://cloud.iexapis.com/stable/stock/aapl/quote/?token=YOUR_API_KEY'\n     response = requests.get(url)\n     data = response.json()\n     print(data)\n     ```\n\n4. **pandas-datareader**：\n   - 这个库可以用于从多种金融数据源获取数据，包括 Yahoo Finance、Google Finance 等。\n   - 安装方法：\n     ```bash\n     pip install pandas-datareader\n     ```\n   - 使用示例：\n     ```python\n     import pandas_datareader as pdr\n     from datetime import datetime\n     \n     start = datetime(2023, 1, 1)\n     end = datetime(2023, 10, 1)\n     data = pdr.get_data_yahoo('AAPL', start, end)\n     print(data)\n     ```\n\n注意：不同的 API 或库可能有不同的数据更新频率和限制，使用之前请查阅相应的文档。此外，一些免费API可能存在请求次数的限制，如果需要更大量的数据或者更高频率的更新，可能需要考虑付费方案。", "cmd": "chat", "custom_id": "159105", "updated_at": "2025-08-04 14:12:53"}
{"timestamp": 1754288049.7764242, "datetime": "2025-08-04 14:14:09", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "bag", "cmd": "chat"}
{"timestamp": 1754288202.807902, "datetime": "2025-08-04 14:16:42", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754288203.7844815, "datetime": "2025-08-04 14:16:43", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wordle start", "cmd": "chat"}
{"timestamp": 1754288205.804961, "datetime": "2025-08-04 14:16:45", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "🎯 Wordle 游戏开始！猜一个5字母英文单词\n \n![Wordle Game](https://i.gyazo.com/2fa299c0a3d5afe5aa6a1a01812143a5.png)\n \nWordle 游戏 (0/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754288211.2245646, "datetime": "2025-08-04 14:16:51", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "biger", "cmd": "chat"}
{"timestamp": 1754288211.4510758, "datetime": "2025-08-04 14:16:51", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su: BIGER 不是一个合法的单词", "cmd": "chat"}
{"timestamp": 1754288219.8000338, "datetime": "2025-08-04 14:16:59", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "finds", "cmd": "chat"}
{"timestamp": 1754288221.257676, "datetime": "2025-08-04 14:17:01", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su 猜测: FINDS\n \n![Wordle Game](https://i.gyazo.com/d6ddc9dadec23a23bd84576b6272874c.png)\n \nWordle 游戏 (1/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754294891.8198721, "datetime": "2025-08-04 16:08:11", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754294894.682611, "datetime": "2025-08-04 16:08:14", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "太好了！su来了！", "cmd": "chat"}
{"timestamp": 1754294904.9374733, "datetime": "2025-08-04 16:08:24", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wordle start", "cmd": "chat"}
{"timestamp": 1754294906.4915857, "datetime": "2025-08-04 16:08:26", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "🎯 Wordle 游戏开始！猜一个5字母英文单词\n \n![Wordle Game](https://i.gyazo.com/2fa299c0a3d5afe5aa6a1a01812143a5.png)\n \nWordle 游戏 (0/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754294918.3579128, "datetime": "2025-08-04 16:08:38", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "cants", "cmd": "chat"}
{"timestamp": 1754294920.0054727, "datetime": "2025-08-04 16:08:39", "room": "lounges", "nick": "BoB", "trip": "BuR9sE", "text": "su 猜测: CANTS\n \n![Wordle Game](https://i.gyazo.com/30622b23e62cc58d93572a433b378ac5.png)\n \nWordle 游戏 (1/6) - 🎮 进行中", "cmd": "chat"}
{"timestamp": 1754296081.0615017, "datetime": "2025-08-04 16:28:01", "room": "lounges", "nick": "BoB_Line", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754296096.699762, "datetime": "2025-08-04 16:28:16", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "@BoB_Line 你好", "cmd": "chat"}
{"timestamp": 1754296096.9952998, "datetime": "2025-08-04 16:28:16", "room": "lounges", "nick": "BoB_Line", "trip": "BuR9sE", "text": "su，你好！有什么我可以帮助你的吗？", "cmd": "chat", "custom_id": "880202", "updated_at": "2025-08-04 16:28:20"}
{"timestamp": 1754300579.0489874, "datetime": "2025-08-04 17:42:59", "room": "lounges", "nick": "BoB_Wave", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754300588.2387023, "datetime": "2025-08-04 17:43:08", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help shop", "cmd": "chat"}
{"timestamp": 1754300599.9061382, "datetime": "2025-08-04 17:43:19", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop history", "cmd": "chat"}
{"timestamp": 1754300600.1750784, "datetime": "2025-08-04 17:43:20", "room": "lounges", "nick": "BoB_Wave", "trip": "BuR9sE", "text": "暂无购买记录", "cmd": "chat"}
{"timestamp": 1754300739.3585773, "datetime": "2025-08-04 17:45:39", "room": "lounges", "nick": "BoB_Calm", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754306350.9359574, "datetime": "2025-08-04 19:19:10", "room": "lounges", "nick": "BoB_Tell", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754306423.130435, "datetime": "2025-08-04 19:20:23", "room": "lounges", "nick": "BoB_Tell", "trip": "BuR9sE", "text": "太好了！su来了！", "cmd": "chat"}
{"timestamp": 1754306446.688039, "datetime": "2025-08-04 19:20:46", "room": "lounges", "nick": "BoB_Tell", "trip": "BuR9sE", "text": "哈喽！su！让我们一起度过美好时光吧！", "cmd": "chat"}
{"timestamp": 1754306449.2296112, "datetime": "2025-08-04 19:20:49", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754306449.4339, "datetime": "2025-08-04 19:20:49", "room": "lounges", "nick": "BoB_Tell", "trip": "BuR9sE", "text": "su 当前拥有 0 金币", "cmd": "chat"}
{"timestamp": 1754306460.3595302, "datetime": "2025-08-04 19:21:00", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754306460.3626037, "datetime": "2025-08-04 19:21:00", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754306461.208131, "datetime": "2025-08-04 19:21:01", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "su 当前拥有 1145141919810 金币", "cmd": "chat"}
{"timestamp": 1754306465.0830545, "datetime": "2025-08-04 19:21:05", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754306465.3213627, "datetime": "2025-08-04 19:21:05", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 1 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754306467.8129373, "datetime": "2025-08-04 19:21:07", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "buy 1", "cmd": "chat"}
{"timestamp": 1754306474.7843316, "datetime": "2025-08-04 19:21:14", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "buy 2", "cmd": "chat"}
{"timestamp": 1754306475.024464, "datetime": "2025-08-04 19:21:15", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "✅ su: 成功购买 签到金币翻倍卡！\n流水单号: ORD306474FHHZ\n花费: 150 金币\n已添加到背包", "cmd": "chat"}
{"timestamp": 1754306481.123464, "datetime": "2025-08-04 19:21:21", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop history", "cmd": "chat"}
{"timestamp": 1754306481.362168, "datetime": "2025-08-04 19:21:21", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "🛒 购买历史:\n• 2025-08-04 19:21:07 - 自定义欢迎语\n  流水号: ORD306467OKCF | 花费: 100金币\n• 2025-08-04 19:21:14 - 签到金币翻倍卡\n  流水号: ORD306474FHHZ | 花费: 150金币", "cmd": "chat"}
{"timestamp": 1754306494.6801329, "datetime": "2025-08-04 19:21:34", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "哇！su！今天也要开开心心的过啊！", "cmd": "chat"}
{"timestamp": 1754306502.6203294, "datetime": "2025-08-04 19:21:42", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wel 苏淋来喽", "cmd": "chat"}
{"timestamp": 1754306502.8631437, "datetime": "2025-08-04 19:21:42", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "❌ su: 你还没有购买自定义欢迎语功能", "cmd": "chat"}
{"timestamp": 1754306512.8550456, "datetime": "2025-08-04 19:21:52", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754306513.526187, "datetime": "2025-08-04 19:21:53", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 1 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754306521.2217252, "datetime": "2025-08-04 19:22:01", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "buy 1 苏淋来喽", "cmd": "chat"}
{"timestamp": 1754306531.4362392, "datetime": "2025-08-04 19:22:11", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop history", "cmd": "chat"}
{"timestamp": 1754306531.658423, "datetime": "2025-08-04 19:22:11", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "🛒 购买历史:\n• 2025-08-04 19:21:07 - 自定义欢迎语\n  流水号: ORD306467OKCF | 花费: 100金币\n• 2025-08-04 19:21:14 - 签到金币翻倍卡\n  流水号: ORD306474FHHZ | 花费: 150金币\n• 2025-08-04 19:22:01 - 自定义欢迎语\n  流水号: ORD306521BAR0 | 花费: 100金币", "cmd": "chat"}
{"timestamp": 1754306535.297868, "datetime": "2025-08-04 19:22:15", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "ls", "cmd": "chat"}
{"timestamp": 1754306602.4758275, "datetime": "2025-08-04 19:23:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help all", "cmd": "chat"}
{"timestamp": 1754306647.1336408, "datetime": "2025-08-04 19:24:07", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wel on", "cmd": "chat"}
{"timestamp": 1754306647.3707502, "datetime": "2025-08-04 19:24:07", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "su: 你还没有购买自定义欢迎语功能", "cmd": "chat"}
{"timestamp": 1754306694.545659, "datetime": "2025-08-04 19:24:54", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock", "cmd": "chat"}
{"timestamp": 1754306703.0634878, "datetime": "2025-08-04 19:25:03", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wordle", "cmd": "chat"}
{"timestamp": 1754306719.72715, "datetime": "2025-08-04 19:25:19", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock list", "cmd": "chat"}
{"timestamp": 1754306735.2078936, "datetime": "2025-08-04 19:25:35", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock portfolio", "cmd": "chat"}
{"timestamp": 1754306798.986399, "datetime": "2025-08-04 19:26:38", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "ping", "cmd": "chat"}
{"timestamp": 1754306799.2187996, "datetime": "2025-08-04 19:26:39", "room": "lounges", "nick": "BoB_Word", "trip": "BuR9sE", "text": "✅ su 签到成功！ 🥇 第1名\n💰 获得金币: 60 (翻倍卡生效)\n   基础奖励: 10 + 排名奖励: 20\n🔥 连续签到: 1天\n💎 当前金币: 1145141919520", "cmd": "chat"}
{"timestamp": 1754306945.4476702, "datetime": "2025-08-04 19:29:05", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock buy GOOGL 1000", "cmd": "chat"}
{"timestamp": 1754306945.7292705, "datetime": "2025-08-04 19:29:05", "room": "lounges", "nick": "BoB_Use", "trip": "BuR9sE", "text": "✅ su: 成功买入 1000 股 谷歌母公司(GOOGL)，单价 $128.27，总计花费 128270 金币\n💰 剩余金币: 1145141791250", "cmd": "chat"}
{"timestamp": 1754306960.834753, "datetime": "2025-08-04 19:29:20", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock", "cmd": "chat"}
{"timestamp": 1754306973.80432, "datetime": "2025-08-04 19:29:33", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock list", "cmd": "chat"}
{"timestamp": 1754306994.5612156, "datetime": "2025-08-04 19:29:54", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock sell GOOGL 1000", "cmd": "chat"}
{"timestamp": 1754306994.8478813, "datetime": "2025-08-04 19:29:54", "room": "lounges", "nick": "BoB_Use", "trip": "BuR9sE", "text": "✅ su: 成功卖出 1000 股 谷歌母公司(GOOGL)，单价 $127.74，总计获得 127740 金币\n💰 当前金币: 1145141918990", "cmd": "chat"}
{"timestamp": 1754307126.0955298, "datetime": "2025-08-04 19:32:06", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock buy NIO 100000", "cmd": "chat"}
{"timestamp": 1754307126.407754, "datetime": "2025-08-04 19:32:06", "room": "lounges", "nick": "BoB_Free", "trip": "BuR9sE", "text": "✅ su: 成功买入 100000 股 蔚来汽车(NIO)，单价 $8.26，总计花费 826000 金币\n💰 剩余金币: 1145141092990", "cmd": "chat"}
{"timestamp": 1754307591.4398396, "datetime": "2025-08-04 19:39:51", "room": "lounges", "nick": "BoB_Data", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754307592.8721504, "datetime": "2025-08-04 19:39:52", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754308501.254008, "datetime": "2025-08-04 19:55:01", "room": "lounges", "nick": "BoB_Day", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754308525.486699, "datetime": "2025-08-04 19:55:25", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "@BoB_Day 你好", "cmd": "chat"}
{"timestamp": 1754308525.7160916, "datetime": "2025-08-04 19:55:25", "room": "lounges", "nick": "BoB_Day", "trip": "BuR9sE", "text": "su，你好！很高兴再次见到你。有任何问题或需要帮助的地方吗？", "cmd": "chat", "custom_id": "519839", "updated_at": "2025-08-04 19:55:30"}
{"timestamp": 1754308547.8817554, "datetime": "2025-08-04 19:55:47", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754308556.5300548, "datetime": "2025-08-04 19:55:56", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help rank", "cmd": "chat"}
{"timestamp": 1754308559.9796016, "datetime": "2025-08-04 19:55:59", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "rank", "cmd": "chat"}
{"timestamp": 1754308560.9132001, "datetime": "2025-08-04 19:56:00", "room": "lounges", "nick": "BoB_Day", "trip": "BuR9sE", "text": "请指定排行榜类型:\n`rank 1` - 金币排行榜\n`rank 2` - Wordle计时排行榜\n`rank 3` - 成语接龙排行榜\n`rank <用户名>` - 查看用户排名", "cmd": "chat"}
{"timestamp": 1754308576.604047, "datetime": "2025-08-04 19:56:16", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wordle", "cmd": "chat"}
{"timestamp": 1754308583.1015897, "datetime": "2025-08-04 19:56:23", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help wordle", "cmd": "chat"}
{"timestamp": 1754308590.9776354, "datetime": "2025-08-04 19:56:30", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "idiom", "cmd": "chat"}
{"timestamp": 1754308591.635099, "datetime": "2025-08-04 19:56:31", "room": "lounges", "nick": "BoB_Day", "trip": "BuR9sE", "text": "成语接龙命令格式错误", "cmd": "chat"}
{"timestamp": 1754308594.5592322, "datetime": "2025-08-04 19:56:34", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754308594.7913399, "datetime": "2025-08-04 19:56:34", "room": "lounges", "nick": "BoB_Day", "trip": "BuR9sE", "text": "🛒 商店物品:\n[1] 自定义欢迎语 - 100 金币\n    设置专属的个人欢迎语\n    更新价格: 1 金币\n\n[2] 签到金币翻倍卡 - 150 金币\n    下次签到获得的金币翻倍\n\n[3] 成语接龙跳过卡 - 80 金币\n    在成语接龙游戏中跳过一次轮次\n\n[4] Wordle提示卡 - 60 金币\n    在Wordle游戏中获得一个字母位置提示\n\n[5] 神秘礼盒 - 100 金币\n    随机获得奖励或空奖励 (50%概率)\n\n使用 'shop buy <商品号>' 购买物品\n例如: shop buy 1 你的欢迎语", "cmd": "chat"}
{"timestamp": 1754308598.72759, "datetime": "2025-08-04 19:56:38", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock", "cmd": "chat"}
{"timestamp": 1754308599.7973423, "datetime": "2025-08-04 19:56:39", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "uno", "cmd": "chat"}
{"timestamp": 1754309294.362212, "datetime": "2025-08-04 20:08:14", "room": "lounges", "nick": "BoB_Post", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754309297.166914, "datetime": "2025-08-04 20:08:17", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754309304.1915238, "datetime": "2025-08-04 20:08:24", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help all", "cmd": "chat"}
{"timestamp": 1754309382.3912318, "datetime": "2025-08-04 20:09:42", "room": "lounges", "nick": "BoB_Hand", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754309382.3944385, "datetime": "2025-08-04 20:09:42", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "all", "cmd": "chat"}
{"timestamp": 1754309391.2307417, "datetime": "2025-08-04 20:09:51", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "stock", "cmd": "chat"}
{"timestamp": 1754309396.8401153, "datetime": "2025-08-04 20:09:56", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "all", "cmd": "chat"}
{"timestamp": 1754309601.3171973, "datetime": "2025-08-04 20:13:21", "room": "lounges", "nick": "BoB_Part", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754309602.6550033, "datetime": "2025-08-04 20:13:22", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "all", "cmd": "chat"}
{"timestamp": 1754309613.6104214, "datetime": "2025-08-04 20:13:33", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754309956.5957458, "datetime": "2025-08-04 20:19:16", "room": "lounges", "nick": "BoB_Face", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754309959.7718594, "datetime": "2025-08-04 20:19:19", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754309990.798442, "datetime": "2025-08-04 20:19:50", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754310012.410906, "datetime": "2025-08-04 20:20:12", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "buy 1 欢迎苏淋", "cmd": "chat"}
{"timestamp": 1754310012.7002356, "datetime": "2025-08-04 20:20:12", "room": "lounges", "nick": "BoB_Face", "trip": "BuR9sE", "text": "✅ su: 成功购买 自定义欢迎语！\n流水单号: ORD310012O9TP\n花费: 100 金币", "cmd": "chat"}
{"timestamp": 1754310021.5340397, "datetime": "2025-08-04 20:20:21", "room": "lounges", "nick": "BoB_Face", "trip": "BuR9sE", "text": "欢迎苏淋", "cmd": "chat"}
{"timestamp": 1754310026.991883, "datetime": "2025-08-04 20:20:26", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "wel off", "cmd": "chat"}
{"timestamp": 1754310027.8544424, "datetime": "2025-08-04 20:20:27", "room": "lounges", "nick": "BoB_Face", "trip": "BuR9sE", "text": "su: 已关闭自定义欢迎语", "cmd": "chat"}
{"timestamp": 1754310031.8586147, "datetime": "2025-08-04 20:20:31", "room": "lounges", "nick": "BoB_Face", "trip": "BuR9sE", "text": "嗨！su！今天天气真不错呢！", "cmd": "chat"}
{"timestamp": 1754310172.082026, "datetime": "2025-08-04 20:22:52", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop", "cmd": "chat"}
{"timestamp": 1754310174.1206763, "datetime": "2025-08-04 20:22:54", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "shop ", "cmd": "chat"}
{"timestamp": 1754310707.0903087, "datetime": "2025-08-04 20:31:47", "room": "lounges", "nick": "BoB_Love", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754310715.7593102, "datetime": "2025-08-04 20:31:55", "room": "lounges", "nick": "BoB_Love", "trip": "BuR9sE", "text": "欢迎光临！su！祝你今天过得愉快！", "cmd": "chat"}
{"timestamp": 1754310720.8759527, "datetime": "2025-08-04 20:32:00", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754310746.7276032, "datetime": "2025-08-04 20:32:26", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754311201.2357082, "datetime": "2025-08-04 20:40:01", "room": "lounges", "nick": "BoB_Plus", "trip": "BuR9sE", "text": "Hi，我是BoB", "cmd": "chat"}
{"timestamp": 1754311209.7645748, "datetime": "2025-08-04 20:40:09", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "help", "cmd": "chat"}
{"timestamp": 1754311225.5307324, "datetime": "2025-08-04 20:40:25", "room": "lounges", "nick": "su", "trip": "BuR9sE", "text": "coins", "cmd": "chat"}
{"timestamp": 1754311225.7413726, "datetime": "2025-08-04 20:40:25", "room": "lounges", "nick": "BoB_Plus", "trip": "BuR9sE", "text": "su 当前拥有 1145141092890 金币", "cmd": "chat"}
