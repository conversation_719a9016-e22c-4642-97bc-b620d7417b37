{"users": {"TestUser#testtrip": {"username": "TestUser", "trip": "testtrip", "coins": 910, "custom_welcome": null, "custom_welcome_enabled": false, "wordle_best_time": null, "idiom_max_rounds": 0, "inventory": {"checkin_double_card": 0, "idiom_skip_card": 0, "wordle_hint_card": 0}}, "su#BuR9sE": {"username": "su", "trip": "BuR9sE", "coins": 1145141092890, "custom_welcome": "欢迎苏淋", "custom_welcome_enabled": false, "wordle_best_time": null, "idiom_max_rounds": 0, "inventory": {"checkin_double_card": 0, "idiom_skip_card": 0, "wordle_hint_card": 0}, "stock_portfolio": {"NIO": 100000}}}, "shop_items": {"1": {"id": "custom_welcome", "name": "自定义欢迎语", "description": "设置专属的个人欢迎语", "price": 100, "update_price": 1}, "2": {"id": "checkin_double_card", "name": "签到金币翻倍卡", "description": "下次签到获得的金币翻倍", "price": 150}, "3": {"id": "idiom_skip_card", "name": "成语接龙跳过卡", "description": "在成语接龙游戏中跳过一次轮次", "price": 80}, "4": {"id": "wordle_hint_card", "name": "Wordle提示卡", "description": "在Wordle游戏中获得一个字母位置提示", "price": 60}, "5": {"id": "mystery_box", "name": "神秘礼盒", "description": "随机获得奖励或空奖励 (50%概率)", "price": 100}}, "purchase_records": {"TestUser#testtrip": [{"order_id": "ORD1491397KDL", "username": "TestUser", "trip": "testtrip", "user_key": "TestUser#testtrip", "item_id": "2", "item_name": "签到金币翻倍卡", "price": 150, "timestamp": 1754149139.3589065, "datetime": "2025-08-02 23:38:59", "args": []}], "su#BuR9sE": [{"order_id": "ORD306467OKCF", "username": "su", "trip": "BuR9sE", "user_key": "su#BuR9sE", "item_id": "1", "item_name": "自定义欢迎语", "price": 100, "timestamp": 1754306467.816674, "datetime": "2025-08-04 19:21:07", "args": [[]]}, {"order_id": "ORD306474FHHZ", "username": "su", "trip": "BuR9sE", "user_key": "su#BuR9sE", "item_id": "2", "item_name": "签到金币翻倍卡", "price": 150, "timestamp": 1754306474.7879827, "datetime": "2025-08-04 19:21:14", "args": [[]]}, {"order_id": "ORD306521BAR0", "username": "su", "trip": "BuR9sE", "user_key": "su#BuR9sE", "item_id": "1", "item_name": "自定义欢迎语", "price": 100, "timestamp": 1754306521.2255273, "datetime": "2025-08-04 19:22:01", "args": [["苏淋来喽"]]}, {"order_id": "ORD310012O9TP", "username": "su", "trip": "BuR9sE", "user_key": "su#BuR9sE", "item_id": "1", "item_name": "自定义欢迎语", "price": 100, "timestamp": 1754310012.413172, "datetime": "2025-08-04 20:20:12", "args": ["欢迎苏淋"]}]}, "rankings": {"wordle_time": {}, "idiom_rounds": {}}, "daily_checkin": {"today": "2025-08-04", "checkin_list": [{"user_key": "su#BuR9sE", "username": "su", "time": 1754306798.9881299, "rank": 1, "reward": 60, "used_double_card": true}], "checkin_history": {"TestUser#testtrip": {"last_checkin": "2025-08-02", "consecutive_days": 1, "total_days": 1}, "su#BuR9sE": {"last_checkin": "2025-08-04", "consecutive_days": 1, "total_days": 1}}}}